using System.Text.Json.Serialization;
using System.Text.Json;

namespace SilvrBear_Amazon_Automation.Models
{
    /// <summary>
    /// Custom DateTime converter for Amazon API ISO 8601 format requirements
    /// Amazon expects: yyyy-MM-dd'T'HH:mm'Z', yyyy-MM-dd'T'HH:mm:ss'Z', or yyyy-MM-dd'T'HH:mm:ss.SSS'Z'
    /// </summary>
    public class AmazonDateTimeConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return DateTime.Parse(reader.GetString()!);
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            // Convert to UTC and format as yyyy-MM-dd'T'HH:mm:ss'Z' (Amazon's preferred format)
            writer.WriteStringValue(value.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ"));
        }
    }

    /// <summary>
    /// Custom nullable DateTime converter for Amazon API ISO 8601 format requirements
    /// </summary>
    public class AmazonNullableDateTimeConverter : JsonConverter<DateTime?>
    {
        public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var stringValue = reader.GetString();
            return string.IsNullOrEmpty(stringValue) ? null : DateTime.Parse(stringValue);
        }

        public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
        {
            if (value.HasValue)
            {
                // Convert to UTC and format as yyyy-MM-dd'T'HH:mm:ss'Z' (Amazon's preferred format)
                writer.WriteStringValue(value.Value.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ"));
            }
            else
            {
                writer.WriteNullValue();
            }
        }
    }
    /// <summary>
    /// Request model for generateTransportationOptions API
    /// </summary>
    public class GenerateTransportationOptionsRequest
    {
        [JsonPropertyName("placementOptionId")]
        public string PlacementOptionId { get; set; } = string.Empty;

        [JsonPropertyName("shipmentTransportationConfigurations")]
        public List<ShipmentTransportationConfiguration> ShipmentTransportationConfigurations { get; set; } = new();
    }

    /// <summary>
    /// Shipment transportation configuration for each shipment
    /// </summary>
    public class ShipmentTransportationConfiguration
    {
        [JsonPropertyName("shipmentId")]
        public string ShipmentId { get; set; } = string.Empty;

        [JsonPropertyName("contactInformation")]
        public ContactInformation ContactInformation { get; set; } = new();

        [JsonPropertyName("readyToShipWindow")]
        public ReadyToShipWindow ReadyToShipWindow { get; set; } = new();
    }

    /// <summary>
    /// Contact information for transportation
    /// </summary>
    public class ContactInformation
    {
        [JsonPropertyName("email")]
        public string Email { get; set; } = string.Empty;

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("phoneNumber")]
        public string PhoneNumber { get; set; } = string.Empty;
    }

    /// <summary>
    /// Ready to ship window with start and end dates
    /// </summary>
    public class ReadyToShipWindow
    {
        [JsonPropertyName("start")]
        [JsonConverter(typeof(AmazonDateTimeConverter))]
        public DateTime Start { get; set; }

        [JsonPropertyName("end")]
        [JsonConverter(typeof(AmazonDateTimeConverter))]
        public DateTime End { get; set; }
    }

    /// <summary>
    /// Response model for listTransportationOptions API
    /// </summary>
    public class TransportationOptionsResponse
    {
        [JsonPropertyName("transportationOptions")]
        public List<TransportationOption> TransportationOptions { get; set; } = new();

        [JsonPropertyName("operationId")]
        public string? OperationId { get; set; }
    }

    /// <summary>
    /// Individual transportation option details
    /// </summary>
    public class TransportationOption
    {
        [JsonPropertyName("transportationOptionId")]
        public string TransportationOptionId { get; set; } = string.Empty;

        [JsonPropertyName("carrierName")]
        public string CarrierName { get; set; } = string.Empty;

        [JsonPropertyName("serviceName")]
        public string ServiceName { get; set; } = string.Empty;

        [JsonPropertyName("cost")]
        public decimal Cost { get; set; }

        [JsonPropertyName("currency")]
        public string Currency { get; set; } = string.Empty;

        [JsonPropertyName("estimatedDeliveryDate")]
        [JsonConverter(typeof(AmazonNullableDateTimeConverter))]
        public DateTime? EstimatedDeliveryDate { get; set; }

        [JsonPropertyName("isAmazonPartnered")]
        public bool IsAmazonPartnered { get; set; }

        [JsonPropertyName("shippingMode")]
        public string ShippingMode { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for generateTransportationOptions API
    /// </summary>
    public class GenerateTransportationOptionsResponse
    {
        [JsonPropertyName("operationId")]
        public string OperationId { get; set; } = string.Empty;
    }
}
