using System.Text.Json.Serialization;

namespace SilvrBear_Amazon_Automation.Models
{
    /// <summary>
    /// Request model for generateTransportationOptions API
    /// </summary>
    public class GenerateTransportationOptionsRequest
    {
        [JsonPropertyName("placementOptionId")]
        public string PlacementOptionId { get; set; } = string.Empty;

        [JsonPropertyName("shipmentTransportationConfigurations")]
        public List<ShipmentTransportationConfiguration> ShipmentTransportationConfigurations { get; set; } = new();
    }

    /// <summary>
    /// Shipment transportation configuration for each shipment
    /// </summary>
    public class ShipmentTransportationConfiguration
    {
        [JsonPropertyName("shipmentId")]
        public string ShipmentId { get; set; } = string.Empty;

        [JsonPropertyName("contactInformation")]
        public ContactInformation ContactInformation { get; set; } = new();

        [JsonPropertyName("readyToShipWindow")]
        public ReadyToShipWindow ReadyToShipWindow { get; set; } = new();
    }

    /// <summary>
    /// Contact information for transportation
    /// </summary>
    public class ContactInformation
    {
        [JsonPropertyName("email")]
        public string Email { get; set; } = string.Empty;

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("phoneNumber")]
        public string PhoneNumber { get; set; } = string.Empty;
    }

    /// <summary>
    /// Ready to ship window with start and end dates
    /// </summary>
    public class ReadyToShipWindow
    {
        [JsonPropertyName("start")]
        public DateTime Start { get; set; }

        [JsonPropertyName("end")]
        public DateTime End { get; set; }
    }

    /// <summary>
    /// Response model for listTransportationOptions API
    /// </summary>
    public class TransportationOptionsResponse
    {
        [JsonPropertyName("transportationOptions")]
        public List<TransportationOption> TransportationOptions { get; set; } = new();

        [JsonPropertyName("operationId")]
        public string? OperationId { get; set; }
    }

    /// <summary>
    /// Individual transportation option details
    /// </summary>
    public class TransportationOption
    {
        [JsonPropertyName("transportationOptionId")]
        public string TransportationOptionId { get; set; } = string.Empty;

        [JsonPropertyName("carrierName")]
        public string CarrierName { get; set; } = string.Empty;

        [JsonPropertyName("serviceName")]
        public string ServiceName { get; set; } = string.Empty;

        [JsonPropertyName("cost")]
        public decimal Cost { get; set; }

        [JsonPropertyName("currency")]
        public string Currency { get; set; } = string.Empty;

        [JsonPropertyName("estimatedDeliveryDate")]
        public DateTime? EstimatedDeliveryDate { get; set; }

        [JsonPropertyName("isAmazonPartnered")]
        public bool IsAmazonPartnered { get; set; }

        [JsonPropertyName("shippingMode")]
        public string ShippingMode { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for generateTransportationOptions API
    /// </summary>
    public class GenerateTransportationOptionsResponse
    {
        [JsonPropertyName("operationId")]
        public string OperationId { get; set; } = string.Empty;
    }
}
