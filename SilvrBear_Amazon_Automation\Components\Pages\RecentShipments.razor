@page "/recent-shipments"
@using SilvrBear_Amazon_Automation.Constants
@using SilvrBear_Amazon_Automation.Models
@using SilvrBear_Amazon_Automation.Services
@inject IAmazonSellerApiService AmazonSellerApiService
@inject ILogger<RecentShipments> Logger
@rendermode InteractiveServer

<PageTitle>Recent Shipments</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="bi bi-list-ul"></i>
                Recent Amazon FBA Shipments
            </h1>

            @if (!string.IsNullOrEmpty(statusMessage))
            {
                <div class="alert @(isSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show" role="alert">
                    @statusMessage
                    <button type="button" class="btn-close" @onclick="ClearStatusMessage"></button>
                </div>
            }

            <!-- Recent Shipments Section -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history"></i> Shipments from Last 30 Days
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <p class="mb-0 text-muted">View your recent Amazon FBA shipments and their current status</p>
                        <button type="button" class="btn btn-primary" @onclick="FetchRecentShipments" disabled="@isLoadingShipments">
                            @if (isLoadingShipments)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                <span>Loading...</span>
                            }
                            else
                            {
                                <i class="bi bi-cloud-download"></i>
                                <span>Fetch Recent Shipments</span>
                            }
                        </button>
                    </div>

                    @if (recentShipments != null && recentShipments.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Shipment ID</th>
                                        <th>Shipment Name</th>
                                        <th>Status</th>
                                        <th>Destination</th>
                                        <th>Items</th>
                                        <th>Last Updated</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var shipment in recentShipments)
                                    {
                                        <tr>
                                            <td><code>@shipment.ShipmentId</code></td>
                                            <td>@shipment.ShipmentName</td>
                                            <td>
                                                <span class="badge bg-@GetStatusBadgeColor(shipment.ShipmentStatus)">
                                                    @shipment.ShipmentStatus
                                                </span>
                                            </td>
                                            <td>@shipment.DestinationFulfillmentCenterId</td>
                                            <td>N/A</td>
                                            <td>@(shipment.ConfirmedNeedByDate?.ToString("MMM dd, yyyy HH:mm") ?? "N/A")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i>
                                Showing @recentShipments.Count shipment(s) from the last 30 days
                            </small>
                        </div>
                    }
                    else if (recentShipments != null && !recentShipments.Any())
                    {
                        <div class="text-center py-5 text-muted">
                            <i class="bi bi-inbox display-1"></i>
                            <h4 class="mt-3">No Recent Shipments</h4>
                            <p>No shipments found in the last 30 days</p>
                            <small>Try creating a new shipment using the Image to Shipment processor</small>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5 text-muted">
                            <i class="bi bi-cloud-download display-1"></i>
                            <h4 class="mt-3">Ready to Load</h4>
                            <p>Click "Fetch Recent Shipments" to load your recent Amazon FBA shipments</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // Recent shipments functionality
    private List<GetShipmentResponse>? recentShipments;
    private bool isLoadingShipments = false;
    private string statusMessage = string.Empty;
    private bool isSuccess = false;

    private async Task FetchRecentShipments()
    {
        isLoadingShipments = true;
        
        try
        {
            var oneMonthAgo = DateTime.UtcNow.AddDays(-30);
            
            // Include common shipment statuses as required by Amazon API
            var shipmentStatuses = ShipmentConstants.ShipmentStatus.AllStatuses.ToList();
            
            var response = await AmazonSellerApiService.GetInboundShipmentsAsync(
                shipmentStatusList: shipmentStatuses,
                lastUpdatedAfter: oneMonthAgo,
                lastUpdatedBefore: DateTime.UtcNow);

            if (response.IsSuccess && response.Payload != null)
            {
                recentShipments = response.Payload;
                SetStatusMessage($"Successfully loaded {recentShipments.Count} recent shipments", true);
                Logger.LogInformation("Fetched {Count} recent shipments", recentShipments.Count);
            }
            else
            {
                Logger.LogWarning("Failed to fetch recent shipments: {Errors}", 
                    string.Join(", ", response.Errors.Select(e => e.Message)));
                SetStatusMessage("Failed to fetch recent shipments. Please check your Amazon credentials.", false);
                recentShipments = new List<GetShipmentResponse>();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error fetching recent shipments");
            SetStatusMessage($"Error fetching shipments: {ex.Message}", false);
            recentShipments = new List<GetShipmentResponse>();
        }
        finally
        {
            isLoadingShipments = false;
        }
    }

    private string GetStatusBadgeColor(string status)
    {
        var upperStatus = status?.ToUpper();
        return ShipmentConstants.ShipmentStatus.StatusColors.TryGetValue(upperStatus ?? "", out var color) 
            ? color 
            : "secondary";
    }

    private void SetStatusMessage(string message, bool success)
    {
        statusMessage = message;
        isSuccess = success;
    }

    private void ClearStatusMessage()
    {
        statusMessage = string.Empty;
    }
}
