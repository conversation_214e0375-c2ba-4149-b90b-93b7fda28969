using System.ComponentModel.DataAnnotations;

namespace SilvrBear_Amazon_Automation.Models.Configuration;

/// <summary>
/// Configuration model for Amazon default values
/// </summary>
public class AmazonDefaultsConfig
{
    [Required]
    public string LabelPrepPreference { get; set; } = "SELLER_LABEL";

    [Required]
    public string ShipmentType { get; set; } = "SP";

    [Required]
    public string ItemCondition { get; set; } = "NewItem";

    public bool AreCasesRequired { get; set; } = false;
}
