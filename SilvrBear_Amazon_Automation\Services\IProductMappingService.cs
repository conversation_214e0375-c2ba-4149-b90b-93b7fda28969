using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Interface for product mapping operations
/// </summary>
public interface IProductMappingService
{
    /// <summary>
    /// Loads product mapping from Excel file
    /// </summary>
    /// <param name="filePath">Path to the Excel mapping file</param>
    /// <returns>Product mapping data</returns>
    Task<ProductMappingData> LoadProductMappingAsync(string filePath);

    /// <summary>
    /// Gets all available product names for OpenAI processing
    /// </summary>
    /// <returns>List of product names</returns>
    Task<List<string>> GetAvailableProductNamesAsync();

    /// <summary>
    /// Finds the best matching product for a given name
    /// </summary>
    /// <param name="productName">Product name to match</param>
    /// <param name="barcodeType">Optional barcode type filter</param>
    /// <returns>Best matching product or null if not found</returns>
    Task<ProductMapping?> FindBestMatchAsync(string productName, string? barcodeType = null);

    /// <summary>
    /// Gets product mapping by exact product name
    /// </summary>
    /// <param name="productName">Exact product name</param>
    /// <param name="barcodeType">Optional barcode type filter</param>
    /// <returns>Product mapping or null if not found</returns>
    Task<ProductMapping?> GetProductByNameAsync(string productName, string? barcodeType = null);

    /// <summary>
    /// Gets product mapping by SKU
    /// </summary>
    /// <param name="sku">Product SKU</param>
    /// <returns>Product mapping or null if not found</returns>
    Task<ProductMapping?> GetProductBySKUAsync(string sku);

    /// <summary>
    /// Validates if a product exists in the mapping
    /// </summary>
    /// <param name="productName">Product name to validate</param>
    /// <param name="barcodeType">Optional barcode type filter</param>
    /// <returns>True if product exists</returns>
    Task<bool> ProductExistsAsync(string productName, string? barcodeType = null);

    /// <summary>
    /// Gets exact SKU for a product and barcode type combination from mapping
    /// </summary>
    /// <param name="productName">Product name</param>
    /// <param name="barcodeType">Barcode type</param>
    /// <returns>Exact SKU from mapping</returns>
    Task<string> GetSKUAsync(string productName, string? barcodeType);



    /// <summary>
    /// Gets product weight by name and barcode type
    /// </summary>
    /// <param name="productName">Product name</param>
    /// <param name="barcodeType">Barcode type</param>
    /// <returns>Product weight in kg</returns>
    Task<decimal> GetProductWeightAsync(string productName, string? barcodeType);

}


