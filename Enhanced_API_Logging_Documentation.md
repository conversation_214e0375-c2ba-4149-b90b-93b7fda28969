# Enhanced API Logging Implementation

## Overview
All API calls in the Amazon Seller API integration now include comprehensive debug logging that captures all input and output data in the debug console.

## What Was Enhanced

### 1. AmazonApiClient.cs
- **GET Requests**: Added detailed logging for endpoints, URLs, query parameters, and request headers
- **POST Requests**: Added detailed logging for endpoints, request payloads, and headers  
- **POST Direct Requests**: Enhanced logging for v2024-03-20 API endpoints
- **PUT Requests**: Added comprehensive request logging
- **Response Processing**: Enhanced logging for response status codes, headers, and content
- **Error Handling**: Detailed error response logging with type information

### 2. AmazonSellerApiService.cs
- **Method Entry Logging**: All public methods now log entry with parameter details
- **Parameter Serialization**: Complete input data is serialized and logged
- **Workflow Step Tracking**: Multi-step operations show progress through each phase
- **Result Logging**: All method results include success status, error details, and response payloads
- **Enhanced Error Context**: Errors include method context and enhanced messages

## Logging Patterns

### Request Logging Format
```
=== [REQUEST_TYPE] REQUEST DEBUG ===
Endpoint: /api/endpoint
Full URL: https://api.amazon.com/api/endpoint?param=value
Query Parameters: {"param": "value"}
Request Headers: Authorization: Bearer xyz, Content-Type: application/json
Request Payload: {"key": "value"}
```

### Response Logging Format
```
=== API RESPONSE DEBUG ===
Response Status Code: 200
Response Headers: Content-Type: application/json, Date: ...
Response Content: {"result": "data"}
Response Type: TypeName
=== END RESPONSE DEBUG ===
```

### Method Logging Format
```
=== MethodName DEBUG ===
Method Entry - Parameter1: value1
Parameter2 Details: {"complex": "object"}
...
=== MethodName RESULT ===
Success: true/false
Errors Count: 0
Response Payload: {"response": "data"}
```

### Workflow Logging Format
```
=== CreateInboundShipmentAsync WORKFLOW DEBUG ===
STEP 1: Generating packing options...
STEP 1 SUCCESS: Packing options generated successfully
STEP 2: Listing packing options...
...
=== CreateInboundShipmentAsync WORKFLOW RESULT ===
Final Result - Success: true
```

## Key Methods Enhanced

### 1. CreateInboundShipmentPlanAsync
- Logs all items, address details, and marketplace configuration
- Shows complete request payload before sending
- Logs detailed response data

### 2. UpdateInboundShipmentAsync  
- Logs inbound plan ID and request details
- Shows box count and packing information structure
- Displays complete packing payload

### 3. CreateInboundShipmentAsync (Workflow)
- Step-by-step progress logging through 4-step workflow
- Each step shows entry, progress, and result
- Comprehensive final result summary

### 4. ValidateCredentialsAsync
- Shows marketplace ID and base URL being tested
- Logs validation endpoint and parameters
- Detailed success/failure analysis

### 5. Helper Methods
- GeneratePackingOptionsAsync
- ListPackingOptionsAsync  
- ConfirmPackingOptionAsync
- ConfirmPlacementOptionAsync

All include entry parameters, API calls, and result logging.

## Configuration

### Logging Level
- Set to `Information` level in appsettings.Development.json
- All debug logs use `LogInformation` to ensure visibility
- Error logs use `LogError` with exception details

### JSON Serialization
- Uses System.Text.Json for consistent formatting
- WriteIndented = true for readable output
- Complete object serialization for complex parameters

## Usage Examples

### Test Class Available
- `TestApiLogging.cs` provides example usage
- Demonstrates credential validation logging
- Shows inbound plan creation workflow logging
- Includes usage instructions

### Running Tests
```csharp
// Inject the test class and call methods
await testApiLogging.TestCredentialValidationLogging();
await testApiLogging.TestInboundPlanCreationLogging();
testApiLogging.LogUsageInstructions();
```

## Benefits

1. **Complete Request Tracing**: See exactly what data is sent to Amazon
2. **Response Analysis**: Full response content for debugging API issues  
3. **Workflow Visibility**: Track multi-step operations progress
4. **Error Diagnosis**: Enhanced error messages with context
5. **Parameter Verification**: Confirm input data is correctly formatted
6. **Performance Monitoring**: Request/response timing analysis
7. **Authentication Debugging**: Detailed credential validation logging

## Sample Output Preview

When calling any Amazon API method, you'll see output like:

```
=== CreateInboundShipmentPlanAsync DEBUG ===
Method Entry - Items Count: 2
Items Details: [{"sku":"TEST-001","quantity":10}]
Ship From Address: {"name":"Warehouse","addressLine1":"123 Main St"}
Marketplace ID: A21TJRUUN4KGV

=== POST DIRECT REQUEST DEBUG ===
Endpoint: /inbound/fba/2024-03-20/inboundPlans
Request Payload: {"name":"Inbound Plan 2025-07-02","sourceAddress":{...}}

=== API RESPONSE DEBUG ===
Response Status Code: 201
Response Content: {"inboundPlanId":"ip123","status":"CONFIRMED"}
=== END RESPONSE DEBUG ===

=== CreateInboundShipmentPlanAsync RESULT ===
Success: true
Response Payload: {"inboundPlanId":"ip123","status":"CONFIRMED"}
```

This comprehensive logging ensures complete visibility into all Amazon API interactions for debugging and monitoring purposes.
