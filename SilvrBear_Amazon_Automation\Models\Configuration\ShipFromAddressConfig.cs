using System.ComponentModel.DataAnnotations;

namespace SilvrBear_Amazon_Automation.Models.Configuration;

/// <summary>
/// Configuration model for ship from address
/// </summary>
public class ShipFromAddressConfig
{
    [Required]
    public string Name { get; set; } = string.Empty;

    [Required]
    public string AddressLine1 { get; set; } = string.Empty;

    public string? AddressLine2 { get; set; }

    [Required]
    public string City { get; set; } = string.Empty;

    [Required]
    public string StateOrProvince { get; set; } = string.Empty;

    [Required]
    public string StateOrProvinceCode { get; set; } = string.Empty;

    [Required]
    public string PostalCode { get; set; } = string.Empty;

    [Required]
    public string CountryCode { get; set; } = string.Empty;

    [Required]
    [Phone]
    public string PhoneNumber { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
}
