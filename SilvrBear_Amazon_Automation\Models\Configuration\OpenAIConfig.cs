using System.ComponentModel.DataAnnotations;

namespace SilvrBear_Amazon_Automation.Models.Configuration;

/// <summary>
/// Configuration model for OpenAI settings
/// </summary>
public class OpenAIConfig
{
    [Required]
    public string ApiKey { get; set; } = string.Empty;

    [Required]
    public string VisionModel { get; set; } = "gpt-4o";

    [Required]
    public string TextModel { get; set; } = "gpt-4o";

    [Range(1, 10000)]
    public int MaxTokens { get; set; } = 4000;

    [Range(0.0, 2.0)]
    public double Temperature { get; set; } = 0.1;
}
