using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Interface for high-level inbound shipment operations
/// </summary>
public interface IInboundShipmentService
{
    /// <summary>
    /// Creates a complete inbound shipment with all required steps
    /// </summary>
    /// <param name="request">Shipment creation request</param>
    /// <returns>Shipment response with details</returns>
    Task<InboundShipmentResponse> CreateCompleteInboundShipmentAsync(CreateInboundShipmentRequest request);

    /// <summary>
    /// Gets shipment status and details
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Shipment details</returns>
    Task<InboundShipmentResponse> GetShipmentDetailsAsync(string shipmentId);

    /// <summary>
    /// Gets shipment status and details with inbound plan ID
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Shipment details</returns>
    Task<InboundShipmentResponse> GetShipmentDetailsAsync(string inboundPlanId, string shipmentId);

    /// <summary>
    /// Gets list of all shipments with optional filtering
    /// </summary>
    /// <param name="status">Filter by status</param>
    /// <param name="fromDate">Filter from date</param>
    /// <param name="toDate">Filter to date</param>
    /// <returns>List of shipments</returns>
    Task<List<InboundShipmentResponse>> GetShipmentsAsync(
        ShipmentStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string inboundPlanId = "");

    /// <summary>
    /// Updates shipment with new box information
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <param name="boxes">Box information</param>
    /// <returns>Updated shipment response</returns>
    Task<InboundShipmentResponse> UpdateShipmentBoxesAsync(string shipmentId, List<ShipmentBox> boxes);

    /// <summary>
    /// Confirms and finalizes the shipment for shipping
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Confirmation result</returns>
    Task<InboundShipmentResponse> ConfirmShipmentAsync(string shipmentId);

    /// <summary>
    /// Gets shipping labels for the shipment
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <param name="labelType">Type of labels to generate</param>
    /// <returns>Label data (base64 encoded PDF)</returns>
    Task<string> GetShippingLabelsAsync(string shipmentId, string labelType = "UNIQUE");

    /// <summary>
    /// Validates that all required information is provided for shipment creation
    /// </summary>
    /// <param name="request">Shipment request to validate</param>
    /// <returns>Validation result with errors if any</returns>
    Task<(bool IsValid, List<string> Errors)> ValidateShipmentRequestAsync(CreateInboundShipmentRequest request);

    /// <summary>
    /// Gets available fulfillment centers for the marketplace
    /// </summary>
    /// <returns>List of fulfillment centers</returns>
    Task<List<FulfillmentCenter>> GetAvailableFulfillmentCentersAsync();

    /// <summary>
    /// Estimates shipping costs for the shipment
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Shipping cost estimate</returns>
    Task<decimal?> EstimateShippingCostAsync(string shipmentId);

    /// <summary>
    /// Cancels a shipment if it's in a cancellable state
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Cancellation result</returns>
    Task<bool> CancelShipmentAsync(string shipmentId);
}
