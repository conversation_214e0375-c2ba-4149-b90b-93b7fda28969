using System.Text.RegularExpressions;
using SilvrBear_Amazon_Automation.Constants;
using SilvrBear_Amazon_Automation.Models;
using OfficeOpenXml;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Service for managing product mapping from Excel files
/// </summary>
public class ProductMappingService : IProductMappingService
{
    private readonly ILogger<ProductMappingService> _logger;
    private readonly IWebHostEnvironment _environment;
    private ProductMappingData? _cachedMapping;
    private readonly SemaphoreSlim _loadingSemaphore = new(1, 1);

    public ProductMappingService(ILogger<ProductMappingService> logger, IWebHostEnvironment environment)
    {
        _logger = logger;
        _environment = environment;
        
        // Set EPPlus license context
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    /// <summary>
    /// Loads product mapping from Excel file
    /// </summary>
    public async Task<ProductMappingData> LoadProductMappingAsync(string filePath)
    {
        await _loadingSemaphore.WaitAsync();
        try
        {
            _logger.LogInformation("Loading product mapping from: {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException(string.Format(ShipmentConstants.ErrorMessages.MappingFileNotFound, filePath));
            }

            var mappingData = new ProductMappingData
            {
                SourceFile = filePath,
                LoadedAt = DateTime.UtcNow
            };

            using var package = new ExcelPackage(new FileInfo(filePath));
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();
            
            if (worksheet == null)
            {
                throw new InvalidOperationException("No worksheets found in the Excel file");
            }

            // Assume first row contains headers
            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount <= 1)
            {
                throw new InvalidOperationException("Excel file contains no data rows");
            }

            // Read headers to determine column positions
            var headers = new Dictionary<string, int>();
            var maxColumns = worksheet.Dimension?.Columns ?? 0;
            for (int col = 1; col <= maxColumns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim().ToLower();
                if (!string.IsNullOrEmpty(header))
                {
                    headers[header] = col;
                }
            }

            // Map common header variations - expand weight column search
            var productNameCol = GetColumnIndex(headers, "product name", "productname", "product", "name");
            var skuCol = GetColumnIndex(headers, "sku", "product sku", "item sku");
            var barcodeTypeCol = GetColumnIndex(headers, "barcode type", "barcodetype", "barcode", "type");
            var weightCol = GetColumnIndex(headers, 
                "item weight in kgs", "item weight in kg", "weight", "item weight", "product weight", "unit weight",
                "weight kg", "weight(kg)", "weight (kg)", 
                "weight g", "weight(g)", "weight (g)",
                "wt", "wt kg", "wt(kg)", "wt (kg)",
                "weight in kg", "weight in grams", "item wt", "product wt",
                "net weight", "gross weight", "individual weight");
            var categoryCol = GetColumnIndex(headers, "category", "product category", "type");
            var descriptionCol = GetColumnIndex(headers, "description", "product description", "desc");
            var activeCol = GetColumnIndex(headers, "active", "is active", "status");

            _logger.LogInformation("DEBUG: Column mappings - ProductName: {ProductCol}, SKU: {SkuCol}, Weight: {WeightCol}, BarcodeType: {BarcodeCol}", 
                productNameCol, skuCol, weightCol, barcodeTypeCol);
            _logger.LogInformation("DEBUG: Available headers: {Headers}", string.Join(", ", headers.Keys));

            if (weightCol <= 0)
            {
                _logger.LogWarning("Weight column not found in Excel file. Products will use default weight of {DefaultWeight} kg. Available headers: {Headers}", 
                    ShipmentConstants.Conversions.DefaultItemWeightGrams / 1000m, string.Join(", ", headers.Keys));
            }

            // Read data rows
            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var productName = GetCellValue(worksheet, row, productNameCol);
                    var sku = GetCellValue(worksheet, row, skuCol);

                    if (string.IsNullOrWhiteSpace(productName) || string.IsNullOrWhiteSpace(sku))
                    {
                        _logger.LogWarning("Skipping row {Row}: Missing product name or SKU", row);
                        continue;
                    }

                    var weightValue = GetCellValue(worksheet, row, weightCol);
                    var product = new ProductMapping
                    {
                        ProductName = productName.Trim(),
                        SKU = sku.Trim(),
                        BarcodeType = GetCellValue(worksheet, row, barcodeTypeCol)?.Trim(),
                        WeightKg = ParseWeight(weightValue),
                        Category = GetCellValue(worksheet, row, categoryCol)?.Trim(),
                        Description = GetCellValue(worksheet, row, descriptionCol)?.Trim(),
                        IsActive = ParseActive(GetCellValue(worksheet, row, activeCol))
                    };

                    _logger.LogInformation("WEIGHT DEBUG: Product '{ProductName}' -> Weight: {Weight} kg from raw text: '{WeightText}' (Row {Row}, Col {Col})", 
                        product.ProductName, product.WeightKg, weightValue ?? "null", row, weightCol);

                    mappingData.Products.Add(product);

                    // Build lookup dictionaries
                    var lookupKey = CreateLookupKey(product.ProductName, product.BarcodeType);
                    mappingData.ProductLookup[lookupKey] = product;

                    if (!mappingData.SKULookup.TryGetValue(product.SKU, out List<ProductMapping>? value))
                    {
                        value = new List<ProductMapping>();
                        mappingData.SKULookup[product.SKU] = value;
                    }

                    value.Add(product);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing row {Row}: {Error}", row, ex.Message);
                }
            }

            _cachedMapping = mappingData;
            _logger.LogInformation(ShipmentConstants.SuccessMessages.MappingLoadedSuccessfully, mappingData.Products.Count);

            // Weight analysis debug logging
            var defaultWeight = ShipmentConstants.Conversions.DefaultItemWeightGrams / 1000m;
            var defaultWeightCount = mappingData.Products.Count(p => p.WeightKg == defaultWeight);
            var totalProducts = mappingData.Products.Count;
            var sampleWeights = mappingData.Products.Take(5).Select(p => $"{p.ProductName}={p.WeightKg:F3}kg").ToList();
            
            _logger.LogInformation("WEIGHT ANALYSIS: {TotalProducts} products loaded, {DefaultCount} with default weight ({DefaultWeight} kg), {ActualCount} with actual weights",
                totalProducts, defaultWeightCount, defaultWeight, totalProducts - defaultWeightCount);
            _logger.LogInformation("WEIGHT ANALYSIS: Sample weights: {SampleWeights}", string.Join(", ", sampleWeights));
            
            if (defaultWeightCount == totalProducts)
            {
                _logger.LogError("ALL PRODUCTS HAVE DEFAULT WEIGHT! Weight column was probably not found or parsing failed.");
            }

            return mappingData;
        }
        finally
        {
            _loadingSemaphore.Release();
        }
    }

    /// <summary>
    /// Gets all available product names for OpenAI processing
    /// </summary>
    public async Task<List<string>> GetAvailableProductNamesAsync()
    {
        var mapping = await GetOrLoadMappingAsync();
        return mapping.Products
            .Where(p => p.IsActive)
            .Select(p => p.ProductName)
            .Distinct()
            .OrderBy(name => name)
            .ToList();
    }

    /// <summary>
    /// Finds the best matching product for a given name with enhanced barcode type handling
    /// </summary>
    public async Task<ProductMapping?> FindBestMatchAsync(string productName, string? barcodeType = null)
    {
        var mapping = await GetOrLoadMappingAsync();

        _logger.LogDebug("FindBestMatchAsync called with productName: '{ProductName}', barcodeType: '{BarcodeType}'. Total products in mapping: {TotalProducts}",
            productName, barcodeType ?? "null", mapping.Products.Count);

        // Step 1: Try exact match with barcode type
        var exactMatch = await GetProductByNameAsync(productName, barcodeType);
        if (exactMatch != null)
        {
            _logger.LogDebug("Exact match found: '{ProductName}' with barcode '{BarcodeType}' and weight: {Weight} kg",
                exactMatch.ProductName, exactMatch.BarcodeType ?? "null", exactMatch.WeightKg);
            return exactMatch;
        }

        // Step 2: Enhanced fallback logic for barcode types
        if (!string.IsNullOrEmpty(barcodeType) && (barcodeType.Equals("OLD", StringComparison.CurrentCultureIgnoreCase) || barcodeType.Equals("NEW", StringComparison.CurrentCultureIgnoreCase)))
        {
            _logger.LogDebug("No exact match found with barcode type '{BarcodeType}', trying product-only matches...", barcodeType);

            // Find all products with the same name regardless of barcode type
            var productOnlyMatches = mapping.Products
                .Where(p => p.IsActive && string.Equals(p.ProductName, productName, StringComparison.OrdinalIgnoreCase))
                .ToList();

            if (productOnlyMatches.Count == 1)
            {
                _logger.LogInformation("Found single product match for '{ProductName}', ignoring barcode type '{BarcodeType}'. Using product: '{MappedName}' with barcode '{MappedBarcode}'",
                    productName, barcodeType, productOnlyMatches.First().ProductName, productOnlyMatches.First().BarcodeType ?? "null");
                return productOnlyMatches.First();
            }
            else if (productOnlyMatches.Count > 1)
            {
                _logger.LogDebug("Found {MatchCount} products with name '{ProductName}' but different barcode types. Cannot auto-select.",
                    productOnlyMatches.Count, productName);
            }
        }

        _logger.LogDebug("No exact match found, trying fuzzy matching...");

        // Step 3: Try fuzzy matching with barcode type
        var candidates = mapping.Products
            .Where(p => p.IsActive)
            .Where(p => barcodeType == null || p.BarcodeType == barcodeType)
            .ToList();

        _logger.LogDebug("Found {CandidateCount} candidates for fuzzy matching with barcode filter", candidates.Count);

        var candidatesWithScores = candidates
            .Select(p => new { Product = p, Score = CalculateSimilarity(productName, p.ProductName) })
            .OrderByDescending(x => x.Score)
            .ToList();

        // Debug logging for similarity scores
        _logger.LogDebug("Similarity scores for '{ProductName}':", productName);
        foreach (var candidate in candidatesWithScores.Take(5))
        {
            _logger.LogDebug("  '{CandidateName}' -> Score: {Score:F3}",
                candidate.Product.ProductName, candidate.Score);
        }

        var bestMatch = candidatesWithScores
            .Where(x => x.Score > 0.6) // Lowered threshold for better matching
            .FirstOrDefault();

        if (bestMatch != null)
        {
            _logger.LogDebug("Best fuzzy match found: '{ProductName}' with score: {Score:F2} and weight: {Weight} kg",
                bestMatch.Product.ProductName, bestMatch.Score, bestMatch.Product.WeightKg);
            return bestMatch.Product;
        }

        // Step 4: If no match with barcode type, try fuzzy matching without barcode type filter
        if (!string.IsNullOrEmpty(barcodeType))
        {
            _logger.LogDebug("No fuzzy match found with barcode type, trying without barcode filter...");

            var allCandidates = mapping.Products
                .Where(p => p.IsActive)
                .ToList();

            var bestMatchWithoutBarcode = allCandidates
                .Select(p => new { Product = p, Score = CalculateSimilarity(productName, p.ProductName) })
                .Where(x => x.Score > 0.6) // Lowered threshold for better matching
                .OrderByDescending(x => x.Score)
                .FirstOrDefault();

            if (bestMatchWithoutBarcode != null)
            {
                _logger.LogInformation("Best fuzzy match found without barcode filter: '{ProductName}' with score: {Score:F2}, ignoring barcode type mismatch",
                    bestMatchWithoutBarcode.Product.ProductName, bestMatchWithoutBarcode.Score);
                return bestMatchWithoutBarcode.Product;
            }
        }

        _logger.LogDebug("No match found for product '{ProductName}' with barcode '{BarcodeType}'", productName, barcodeType ?? "null");
        return null;
    }

    /// <summary>
    /// Gets product mapping by exact product name
    /// </summary>
    public async Task<ProductMapping?> GetProductByNameAsync(string productName, string? barcodeType = null)
    {
        var mapping = await GetOrLoadMappingAsync();
        var lookupKey = CreateLookupKey(productName, barcodeType);
        
        return mapping.ProductLookup.TryGetValue(lookupKey, out var product) ? product : null;
    }

    /// <summary>
    /// Gets product mapping by SKU
    /// </summary>
    public async Task<ProductMapping?> GetProductBySKUAsync(string sku)
    {
        var mapping = await GetOrLoadMappingAsync();
        
        if (mapping.SKULookup.TryGetValue(sku, out var products))
        {
            return products.FirstOrDefault(p => p.IsActive) ?? products.FirstOrDefault();
        }

        return null;
    }

    /// <summary>
    /// Validates if a product exists in the mapping
    /// </summary>
    public async Task<bool> ProductExistsAsync(string productName, string? barcodeType = null)
    {
        var product = await GetProductByNameAsync(productName, barcodeType);
        return product != null && product.IsActive;
    }

    /// <summary>
    /// Gets SKU for a product and barcode type combination (exact SKU from mapping only)
    /// </summary>
    public async Task<string> GetSKUAsync(string productName, string? barcodeType)
    {
        var product = await FindBestMatchAsync(productName, barcodeType);
        if (product != null)
        {
            return product.SKU; // Return exact SKU from mapping without any modifications
        }

        // Return empty string if not found in mapping - no generated SKUs
        return string.Empty;
    }



    /// <summary>
    /// Gets product weight by name and barcode type
    /// </summary>
    public async Task<decimal> GetProductWeightAsync(string productName, string? barcodeType)
    {
        var product = await FindBestMatchAsync(productName, barcodeType);
        return product?.WeightKg ?? ShipmentConstants.Conversions.DefaultItemWeightGrams / 1000m;
    }





    #region Private Helper Methods

    private async Task<ProductMappingData> GetOrLoadMappingAsync()
    {
        if (_cachedMapping == null)
        {
            var mappingFilePath = Path.Combine(_environment.ContentRootPath, "Data", ShipmentConstants.FileProcessing.MappingFileName);
            await LoadProductMappingAsync(mappingFilePath);
        }

        return _cachedMapping!;
    }

    private static int GetColumnIndex(Dictionary<string, int> headers, params string[] possibleNames)
    {
        foreach (var name in possibleNames)
        {
            if (headers.TryGetValue(name, out var index))
            {
                return index;
            }
        }
        return -1;
    }

    private static string? GetCellValue(ExcelWorksheet worksheet, int row, int col)
    {
        if (col <= 0) return null;
        return worksheet.Cells[row, col].Text?.Trim();
    }

    private static decimal ParseWeight(string? weightText)
    {
        if (string.IsNullOrWhiteSpace(weightText))
        {
            return ShipmentConstants.Conversions.DefaultItemWeightGrams / 1000m;
        }

        // Clean the weight text by removing common weight units and extra spaces
        var originalText = weightText.Trim();
        var cleanedWeight = weightText
            .Replace("kg", "", StringComparison.OrdinalIgnoreCase)
            .Replace("grams", "", StringComparison.OrdinalIgnoreCase)
            .Replace("gram", "", StringComparison.OrdinalIgnoreCase)
            .Replace("g", "", StringComparison.OrdinalIgnoreCase)
            .Replace("kilogram", "", StringComparison.OrdinalIgnoreCase)
            .Replace("kilo", "", StringComparison.OrdinalIgnoreCase)
            .Replace("lbs", "", StringComparison.OrdinalIgnoreCase)
            .Replace("lb", "", StringComparison.OrdinalIgnoreCase)
            .Replace("pounds", "", StringComparison.OrdinalIgnoreCase)
            .Replace("pound", "", StringComparison.OrdinalIgnoreCase)
            .Trim();

        if (decimal.TryParse(cleanedWeight, out var weight))
        {
            var originalLower = originalText.ToLower();
            
            // Convert based on units in original text
            if (originalLower.Contains("kg") || originalLower.Contains("kilogram") || originalLower.Contains("kilo"))
            {
                // Already in kg
                return weight;
            }
            else if (originalLower.Contains('g') && !originalLower.Contains("kg"))
            {
                // In grams, convert to kg
                return weight / 1000m;
            }
            else if (originalLower.Contains("lb") || originalLower.Contains("pound"))
            {
                // In pounds, convert to kg
                return weight / 2.20462m;
            }
            else
            {
                // No unit specified - use smart detection
                // If weight is >= 10, likely grams; if < 10, likely kg
                // This threshold is more reasonable than 1
                return weight >= 10 ? weight / 1000m : weight;
            }
        }

        return ShipmentConstants.Conversions.DefaultItemWeightGrams / 1000m;
    }

    private static bool ParseActive(string? activeText)
    {
        if (string.IsNullOrWhiteSpace(activeText))
        {
            return true; // Default to active
        }

        return activeText.ToLower() switch
        {
            "true" or "yes" or "y" or "1" or "active" => true,
            "false" or "no" or "n" or "0" or "inactive" => false,
            _ => true
        };
    }

    private static string CreateLookupKey(string productName, string? barcodeType)
    {
        return $"{productName.ToLower()}|{barcodeType?.ToLower() ?? ""}";
    }

    private static double CalculateSimilarity(string text1, string text2)
    {
        if (string.IsNullOrEmpty(text1) || string.IsNullOrEmpty(text2))
        {
            return 0;
        }

        text1 = text1.ToLower().Trim();
        text2 = text2.ToLower().Trim();

        if (text1 == text2)
        {
            return 1.0;
        }

        // Enhanced similarity calculation with multiple approaches

        // 1. Check if one string contains the other (substring match)
        if (text2.Contains(text1) || text1.Contains(text2))
        {
            var shorter = text1.Length < text2.Length ? text1 : text2;
            var longer = text1.Length >= text2.Length ? text1 : text2;
            // Higher score for longer matches
            var lengthRatio = (double)shorter.Length / longer.Length;
            return lengthRatio > 0.7 ? 0.95 : lengthRatio * 0.9; // High score for good substring matches
        }

        // 2. Check word-based similarity (for multi-word product names)
        var words1 = text1.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var words2 = text2.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        if (words1.Length > 1 || words2.Length > 1)
        {
            var wordMatchScore = CalculateWordMatchScore(words1, words2);
            if (wordMatchScore > 0.7)
            {
                return wordMatchScore;
            }
        }

        // 3. Fallback to Levenshtein distance-based similarity
        var distance = LevenshteinDistance(text1, text2);
        var maxLength = Math.Max(text1.Length, text2.Length);

        return 1.0 - (double)distance / maxLength;
    }

    private static double CalculateWordMatchScore(string[] words1, string[] words2)
    {
        if (words1.Length == 0 || words2.Length == 0)
            return 0;

        var matchedWords = 0.0;
        var totalWords = Math.Max(words1.Length, words2.Length);

        foreach (var word1 in words1)
        {
            foreach (var word2 in words2)
            {
                // Exact word match
                if (word1.Equals(word2, StringComparison.OrdinalIgnoreCase))
                {
                    matchedWords += 1.0;
                    break;
                }
                // Partial word match (one contains the other)
                else if (word1.Length >= 3 && word2.Length >= 3 &&
                        (word1.Contains(word2) || word2.Contains(word1)))
                {
                    matchedWords += 0.8; // Partial credit for partial matches
                    break;
                }
            }
        }

        return matchedWords / totalWords;
    }

    private static int LevenshteinDistance(string s1, string s2)
    {
        var matrix = new int[s1.Length + 1, s2.Length + 1];

        for (int i = 0; i <= s1.Length; i++)
            matrix[i, 0] = i;

        for (int j = 0; j <= s2.Length; j++)
            matrix[0, j] = j;

        for (int i = 1; i <= s1.Length; i++)
        {
            for (int j = 1; j <= s2.Length; j++)
            {
                var cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
                matrix[i, j] = Math.Min(
                    Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                    matrix[i - 1, j - 1] + cost);
            }
        }

        return matrix[s1.Length, s2.Length];
    }

    #endregion
}
