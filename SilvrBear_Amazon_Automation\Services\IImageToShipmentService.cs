using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Interface for complete image to shipment processing
/// </summary>
public interface IImageToShipmentService
{

    /// <summary>
    /// Extracts JSON data from image without processing or mapping
    /// </summary>
    /// <param name="imageFile">Image file to process</param>
    /// <param name="shipmentName">Optional shipment name</param>
    /// <param name="fulfillmentCenterId">Optional fulfillment center ID</param>
    /// <returns>Result with extracted JSON data</returns>
    Task<ImageToShipmentResult> ExtractJsonFromImageAsync(
        IFormFile imageFile,
        string? shipmentName = null,
        string? fulfillmentCenterId = null);

    /// <summary>
    /// Processes extracted JSON data and maps to products
    /// </summary>
    /// <param name="extractedBoxData">Box data from JSON extraction</param>
    /// <param name="shipmentName">Optional shipment name</param>
    /// <param name="fulfillmentCenterId">Optional fulfillment center ID</param>
    /// <returns>Processing result</returns>
    Task<ImageToShipmentResult> ProcessExtractedDataAsync(
        List<BoxDataRow> extractedBoxData,
        string? shipmentName = null,
        string? fulfillmentCenterId = null);

    /// <summary>
    /// Processes an image and validates data without creating shipment
    /// </summary>
    /// <param name="imageFile">Image file to process</param>
    /// <param name="shipmentName">Optional shipment name</param>
    /// <param name="fulfillmentCenterId">Optional fulfillment center ID</param>
    /// <returns>Validation result</returns>
    Task<ImageToShipmentResult> ValidateImageDataAsync(
        IFormFile imageFile,
        string? shipmentName = null,
        string? fulfillmentCenterId = null);

    /// <summary>
    /// Processes extracted box data and maps to products
    /// </summary>
    /// <param name="processedBoxes">Processed box data from image</param>
    /// <returns>Mapped and enriched box data</returns>
    Task<List<ProcessedBoxData>> MapAndEnrichBoxDataAsync(List<ProcessedBoxData> processedBoxes);

    /// <summary>
    /// Converts processed box data to shipment request
    /// </summary>
    /// <param name="processedBoxes">Processed and mapped box data</param>
    /// <param name="shipmentName">Shipment name</param>
    /// <param name="fulfillmentCenterId">Fulfillment center ID</param>
    /// <returns>Create inbound shipment request</returns>
    Task<CreateInboundShipmentRequest> ConvertToShipmentRequestAsync(
        List<ProcessedBoxData> processedBoxes,
        string shipmentName,
        string fulfillmentCenterId);

    /// <summary>
    /// Calculates box weights and dimensions
    /// </summary>
    /// <param name="processedBox">Box data to calculate</param>
    /// <returns>Updated box data with calculations</returns>
    Task<ProcessedBoxData> CalculateBoxMetricsAsync(ProcessedBoxData processedBox);

    /// <summary>
    /// Validates complete shipment data before creation
    /// </summary>
    /// <param name="shipmentRequest">Shipment request to validate</param>
    /// <returns>Validation result</returns>
    Task<(bool IsValid, List<string> Errors, List<string> Warnings)> ValidateShipmentDataAsync(
        CreateInboundShipmentRequest shipmentRequest);

}


