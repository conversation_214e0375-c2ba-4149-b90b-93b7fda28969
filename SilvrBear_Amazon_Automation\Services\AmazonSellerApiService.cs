using Microsoft.Extensions.Options;
using SilvrBear_Amazon_Automation.Models;
using SilvrBear_Amazon_Automation.Constants;
using System.Text.Json;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Implementation of Amazon Seller API service using Fulfillment Inbound API v2024-03-20
/// </summary>
public class AmazonSellerApiService : IAmazonSellerApiService
{
    private readonly AmazonApiClient _apiClient;
    private readonly AmazonCredentials _credentials;
    private readonly ILogger<AmazonSellerApiService> _logger;

    public AmazonSellerApiService(
        AmazonApiClient apiClient,
        IOptions<AmazonCredentials> credentials,
        ILogger<AmazonSellerApiService> logger)
    {
        _apiClient = apiClient;
        _credentials = credentials.Value;
        _logger = logger;
    }

    /// <summary>
    /// Polls operation status until completion or timeout
    /// This method should be called after any Amazon API call that returns an operationId
    /// </summary>
    /// <param name="operationId">The operation ID to monitor</param>
    /// <param name="operationName">Human-readable name for logging (e.g., "plan creation", "placement options generation")</param>
    /// <param name="maxStatusChecks">Maximum number of status checks (default: 15)</param>
    /// <param name="statusCheckDelayMs">Delay between status checks in milliseconds (default: 10000)</param>
    /// <returns>True if operation completed successfully, false if failed or timed out</returns>
    public async Task<(bool Success, string? ErrorMessage)> PollOperationStatusAsync(
        string operationId,
        string operationName,
        int maxStatusChecks = 15,
        int statusCheckDelayMs = 10000)
    {
        if (string.IsNullOrEmpty(operationId))
        {
            _logger.LogInformation("No operation ID provided for {OperationName}, skipping status monitoring", operationName);
            return (true, null); // No operation ID means immediate success
        }

        _logger.LogInformation("=== OPERATION STATUS MONITORING: {OperationName} ===", operationName);
        _logger.LogInformation("Monitoring operation ID: {OperationId} for {OperationName}", operationId, operationName);

        for (int i = 0; i < maxStatusChecks; i++)
        {
            _logger.LogInformation("Status check {Attempt}/{MaxAttempts} for {OperationName} operation {OperationId}",
                i + 1, maxStatusChecks, operationName, operationId);

            var operationStatus = await GetInboundOperationStatusAsync(operationId);
            if (operationStatus.IsSuccess && operationStatus.Payload != null)
            {
                var status = operationStatus.Payload.OperationStatus;
                _logger.LogInformation("{OperationName} operation status: '{Status}'", operationName, status);

                if (status == "SUCCESS" || status == "COMPLETED" || status == "COMPLETE")
                {
                    _logger.LogInformation("✅ {OperationName} operation completed successfully!", operationName);
                    return (true, null);
                }
                else if (status == "FAILED" || status == "ERROR")
                {
                    var errorMessage = $"{operationName} operation failed with status: {status}";
                    _logger.LogError("❌ {ErrorMessage}", errorMessage);

                    // Check for operation problems
                    if (operationStatus.Payload.OperationProblems?.Any() == true)
                    {
                        var problems = string.Join(", ", operationStatus.Payload.OperationProblems.Select(p => p.Message));
                        errorMessage += $". Problems: {problems}";
                        _logger.LogError("Operation problems: {Problems}", problems);
                    }

                    return (false, errorMessage);
                }
                else if (status == "IN_PROGRESS" || status == "PROCESSING")
                {
                    _logger.LogInformation("⏳ {OperationName} operation still in progress, status: {Status}", operationName, status);
                }
                else
                {
                    _logger.LogInformation("❓ Unknown {OperationName} operation status: {Status}", operationName, status);
                }
            }
            else
            {
                _logger.LogWarning("⚠️ Failed to get {OperationName} operation status - Success: {Success}, Errors: {Errors}",
                    operationName,
                    operationStatus.IsSuccess,
                    string.Join(", ", operationStatus.Errors?.Select(e => e.Message) ?? new List<string>()));
            }

            if (i < maxStatusChecks - 1) // Don't wait on the last iteration
            {
                _logger.LogInformation("Waiting {DelayMs}ms before next status check...", statusCheckDelayMs);
                await Task.Delay(statusCheckDelayMs);
            }
        }

        var timeoutMessage = $"{operationName} operation did not complete within {maxStatusChecks} status checks";
        _logger.LogError("⏰ {TimeoutMessage}", timeoutMessage);
        return (false, timeoutMessage);
    }

    /// <summary>
    /// Creates an inbound plan using the new v2024-03-20 API
    /// This replaces the old createInboundShipmentPlan operation
    /// </summary>
    public async Task<AmazonApiResponse<InboundShipmentPlanResponse>> CreateInboundShipmentPlanAsync(
        List<ShipmentItem> items,
        Address shipFromAddress,
        string labelPrepPreference = "SELLER_LABEL")
    {
        try
        {
            
            // Step 1: Create inbound plan using new v2024-03-20 API
            var endpoint = "/inbound/fba/2024-03-20/inboundPlans";

            var requestPayload = new
            {
                name = $"Inbound Plan {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}",
                sourceAddress = new
                {
                    name = shipFromAddress.Name,
                    addressLine1 = shipFromAddress.AddressLine1,
                    addressLine2 = shipFromAddress.AddressLine2,
                    city = shipFromAddress.City,
                    stateOrProvinceCode = shipFromAddress.StateOrProvinceCode,
                    countryCode = shipFromAddress.CountryCode,
                    postalCode = shipFromAddress.PostalCode,
                    phoneNumber = shipFromAddress.PhoneNumber // Required for v2024-03-20 API
                },
                destinationMarketplaces = new[] { _credentials.MarketplaceId },
                items = items.Select(item => new
                {
                    msku = item.Sku,
                    quantity = item.Quantity,
                    labelOwner = labelPrepPreference == "SELLER_LABEL" ? "SELLER" : "AMAZON",
                    prepOwner = "NONE"
                }).ToList()
            };

            _logger.LogInformation("Request Payload Prepared: {Payload}", System.Text.Json.JsonSerializer.Serialize(requestPayload));

            var result = await _apiClient.PostDirectAsync<InboundShipmentPlanResponse>(endpoint, requestPayload);
            
            _logger.LogInformation("=== CreateInboundShipmentPlanAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating inbound plan");
            throw;
        }
    }

    /// <summary>
    /// Creates an inbound plan with specific destination for India marketplace using the new v2024-03-20 API
    /// This includes CustomPlacement details required for IN marketplace
    /// </summary>
    public async Task<AmazonApiResponse<InboundShipmentPlanResponse>> CreateInboundShipmentPlanAsync(
        List<ShipmentItem> items,
        Address shipFromAddress,
        string destinationFulfillmentCenterId,
        string labelPrepPreference = "SELLER_LABEL")
    {
        try
        {
            // Step 1: Create inbound plan using new v2024-03-20 API with CustomPlacement for India
            var endpoint = "/inbound/fba/2024-03-20/inboundPlans";

            var requestPayload = new
            {
                name = $"Inbound Plan {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}",
                sourceAddress = new
                {
                    name = shipFromAddress.Name,
                    addressLine1 = shipFromAddress.AddressLine1,
                    addressLine2 = shipFromAddress.AddressLine2,
                    city = shipFromAddress.City,
                    stateOrProvinceCode = shipFromAddress.StateOrProvinceCode,
                    countryCode = shipFromAddress.CountryCode,
                    postalCode = shipFromAddress.PostalCode,
                    phoneNumber = shipFromAddress.PhoneNumber // Required for v2024-03-20 API
                },
                destinationMarketplaces = new[] { _credentials.MarketplaceId },
                items = items.Select(item => new
                {
                    msku = item.Sku,
                    quantity = item.Quantity,
                    labelOwner = labelPrepPreference == "SELLER_LABEL" ? "SELLER" : "AMAZON",
                    prepOwner = "NONE"
                }).ToList()
                // NOTE: For India marketplace, destination fulfillment center is specified in Step 2 (generatePlacementOptions)
            };

            _logger.LogInformation("Creating inbound plan with {ItemCount} items for India marketplace (FC: {FulfillmentCenter}) using v2024-03-20 API", 
                items.Count, destinationFulfillmentCenterId);

            return await _apiClient.PostDirectAsync<InboundShipmentPlanResponse>(endpoint, requestPayload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating inbound plan for India marketplace");
            throw;
        }
    }

    /// <summary>
    /// Creates an inbound shipment using the Amazon-compliant v2024-03-20 API workflow for India marketplace
    /// Follows Amazon's documented sequence: Generate Placement → List Placement → Set Packing → Confirm Placement
    /// </summary>
    public async Task<AmazonApiResponse<CreateInboundShipmentResponse>> CreateInboundShipmentAsync(
        string inboundPlanId,
        CreateInboundShipmentRequest request)
    {
        try
        {
            _logger.LogInformation("=== CreateInboundShipmentAsync AMAZON COMPLIANT WORKFLOW ===");
            _logger.LogInformation("Starting Amazon-compliant inbound shipment creation for plan {InboundPlanId} with name {ShipmentName}",
                inboundPlanId, request.ShipmentName);
            _logger.LogInformation("Request Details: {Request}", System.Text.Json.JsonSerializer.Serialize(request));

            // STEP 1: Generate placement options (Amazon documented Step 2)
            var generatePlacementResponse = await GeneratePlacementOptionsAsync(inboundPlanId,
                request.DestinationFulfillmentCenterId,
                request.Items?.Select(item => new ShipmentItem { Sku = item.Sku, Quantity = item.Quantity }).ToList());
            if (!generatePlacementResponse.IsSuccess)
            {
                _logger.LogWarning("STEP 1 FAILED: Failed to generate placement options for plan {InboundPlanId}: {Errors}",
                    inboundPlanId, string.Join(", ", generatePlacementResponse.Errors.Select(e => e.Message)));
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Errors = EnhanceWorkflowErrors(generatePlacementResponse.Errors, "placement options generation")
                };
            }
            _logger.LogInformation("STEP 1 SUCCESS: Placement options generated successfully");

            // STEP 1.1: Monitor operation status if we have an operation ID
            string? placementOperationId = null;
            if (generatePlacementResponse.Payload != null)
            {
                try
                {
                    var placementResponseJson = System.Text.Json.JsonSerializer.Serialize(generatePlacementResponse.Payload);
                    _logger.LogInformation("STEP 3.6: Checking for operation ID in placement generation response: {Response}", placementResponseJson);
                    
                    // Try to extract operation ID from the response
                    var responseElement = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(placementResponseJson);
                    if (responseElement.TryGetProperty("operationId", out var operationIdElement))
                    {
                        placementOperationId = operationIdElement.GetString();
                        if (!string.IsNullOrEmpty(placementOperationId))
                        {
                            _logger.LogInformation("STEP 1.1: Found operation ID: {OperationId}, waiting for completion...", placementOperationId);

                            // Poll operation status until complete
                            var maxStatusChecks = 15; // Increased from 10
                            var statusCheckDelay = 10000; // Increased to 10 seconds
                            var operationComplete = false;

                            for (int i = 0; i < maxStatusChecks; i++)
                            {
                                _logger.LogInformation("STEP 1.1: Status check {Attempt}/{MaxAttempts} for operation {OperationId}",
                                    i + 1, maxStatusChecks, placementOperationId);

                                var operationStatus = await GetInboundOperationStatusAsync(placementOperationId);
                                if (operationStatus.IsSuccess && operationStatus.Payload != null)
                                {
                                    var statusJson = System.Text.Json.JsonSerializer.Serialize(operationStatus.Payload);
                                    _logger.LogInformation("STEP 1.1: Operation status response: {Status}", statusJson);

                                    // Check if operation is complete
                                    var statusElement = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(statusJson);
                                    if (statusElement.TryGetProperty("status", out var statusValue))
                                    {
                                        var status = statusValue.GetString();
                                        _logger.LogInformation("STEP 1.1: Operation status value: '{StatusValue}'", status);

                                        if (status == "SUCCESS" || status == "COMPLETED" || status == "COMPLETE")
                                        {
                                            _logger.LogInformation("STEP 1.1: Operation completed successfully!");
                                            operationComplete = true;
                                            break;
                                        }
                                        else if (status == "FAILED" || status == "ERROR")
                                        {
                                            _logger.LogError("STEP 1.1: Operation failed with status: {Status}", status);
                                            // Check for error details
                                            if (statusElement.TryGetProperty("issues", out var issuesElement))
                                            {
                                                _logger.LogError("STEP 1.1: Operation issues: {Issues}", issuesElement.ToString());
                                            }
                                            break;
                                        }
                                        else if (status == "IN_PROGRESS" || status == "PROCESSING")
                                        {
                                            _logger.LogInformation("STEP 1.1: Operation still in progress, status: {Status}", status);
                                        }
                                        else
                                        {
                                            _logger.LogInformation("STEP 1.1: Unknown operation status: {Status}", status);
                                        }
                                    }
                                    else
                                    {
                                        _logger.LogWarning("STEP 1.1: No 'status' property found in operation response");
                                    }
                                }
                                else
                                {
                                    _logger.LogWarning("STEP 1.1: Failed to get operation status - Success: {Success}, Errors: {Errors}",
                                        operationStatus.IsSuccess,
                                        string.Join(", ", operationStatus.Errors?.Select(e => e.Message) ?? new List<string>()));
                                }

                                if (i < maxStatusChecks - 1) // Don't wait on the last iteration
                                {
                                    _logger.LogInformation("STEP 1.1: Waiting {DelayMs}ms before next status check...", statusCheckDelay);
                                    await Task.Delay(statusCheckDelay);
                                }
                            }

                            if (!operationComplete)
                            {
                                _logger.LogWarning("STEP 1.1: Operation did not complete within {MaxChecks} status checks. Will proceed with placement options listing anyway.", maxStatusChecks);
                            }
                            else
                            {
                                _logger.LogInformation("STEP 1.1: Operation completed successfully, proceeding to list placement options.");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "STEP 1.1: Could not check operation status");
                }
            }

            // STEP 2: List placement options to get the actual placementOptionId (Amazon documented Step 3)
            // Note: Adding retry logic with longer delays to ensure placement options are ready
            _logger.LogInformation("STEP 2: Listing placement options (Amazon Step 3) with enhanced retry logic...");
            
            AmazonApiResponse<PlacementOptionsResponse>? placementOptions = null;
            var maxRetries = 5; // Increased from 3
            var delaySeconds = new[] { 5, 10, 15, 20, 30 }; // Longer delays
            
            for (int attempt = 0; attempt < maxRetries; attempt++)
            {
                if (attempt > 0)
                {
                    _logger.LogInformation("STEP 2 RETRY {Attempt}/{MaxRetries}: Waiting {DelaySeconds} seconds before retry...",
                        attempt + 1, maxRetries, delaySeconds[attempt - 1]);
                    await Task.Delay(delaySeconds[attempt - 1] * 1000);
                }

                _logger.LogInformation("STEP 2 ATTEMPT {Attempt}: Listing placement options...", attempt + 1);
                placementOptions = await ListPlacementOptionsAsync(inboundPlanId, pageSize: 1);

                if (placementOptions.IsSuccess && placementOptions.Payload?.PlacementOptions?.Any() == true)
                {
                    _logger.LogInformation("STEP 2 SUCCESS: Found {Count} placement options on attempt {Attempt}",
                        placementOptions.Payload.PlacementOptions.Count, attempt + 1);
                    break;
                }
                else if (placementOptions.IsSuccess)
                {
                    _logger.LogWarning("STEP 2 ATTEMPT {Attempt}: API call successful but no placement options found yet", attempt + 1);
                }
                else
                {
                    _logger.LogWarning("STEP 2 ATTEMPT {Attempt}: API call failed: {Errors}",
                        attempt + 1, string.Join(", ", placementOptions.Errors?.Select(e => e.Message) ?? new List<string>()));
                }
            }

            // Check final result
            if (placementOptions == null || !placementOptions.IsSuccess)
            {
                _logger.LogWarning("STEP 2 FAILED: Failed to list placement options for plan {InboundPlanId} after {MaxRetries} attempts",
                    inboundPlanId, maxRetries);
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Errors = placementOptions?.Errors ?? new List<ApiError> { new() { Code = "PlacementOptionsNotFound", Message = "Could not retrieve placement options after multiple attempts" } }
                };
            }
            
            // Additional check: if we have a successful response but no placement options, provide better error message
            if (placementOptions.Payload?.PlacementOptions?.Any() != true)
            {
                var errorMessage = !string.IsNullOrEmpty(placementOperationId) 
                    ? $"Placement options generation operation {placementOperationId} completed but no placement options are available. This may indicate an issue with the custom placement configuration or the specified fulfillment center '{request.DestinationFulfillmentCenterId}'."
                    : "No placement options available. This may indicate an issue with the inbound plan configuration.";
                    
                _logger.LogWarning("STEP 2 FAILED: {ErrorMessage}", errorMessage);
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Errors = new List<ApiError> { new() { Code = "NoPlacementOptionsAvailable", Message = errorMessage } }
                };
            }
            _logger.LogInformation("STEP 2 SUCCESS: Placement options listed successfully");



            // STEP 3: Set packing information BEFORE confirming placement (Amazon requirement for India)
            if (request.Boxes?.Any() == true)
            {
                _logger.LogInformation("STEP 3: Setting packing information (Amazon Step 4 - required before placement confirmation)...");

                // Extract shipmentId from placement options response
                var shipmentId = placementOptions.Payload?.PlacementOptions?.FirstOrDefault()?.ShipmentIds?.FirstOrDefault();
                _logger.LogInformation("STEP 3: Using shipmentId from placement options: {ShipmentId}", shipmentId ?? "null (will use inboundPlanId)");

                var packingResponse = await UpdateInboundShipmentAsync(inboundPlanId, request, shipmentId);

                if (!packingResponse.IsSuccess)
                {
                    _logger.LogWarning("STEP 3 FAILED: Failed to set packing information for plan {InboundPlanId}: {Errors}",
                        inboundPlanId, string.Join(", ", packingResponse.Errors.Select(e => e.Message)));
                    return new AmazonApiResponse<CreateInboundShipmentResponse>
                    {
                        Errors = packingResponse.Errors
                    };
                }
                _logger.LogInformation("STEP 3 SUCCESS: Packing information set successfully");
            }
            else
            {
                _logger.LogInformation("STEP 3 SKIPPED: No box information provided");
            }

            // STEP 4: Confirm placement option (Amazon documented Step 5)
            _logger.LogInformation("STEP 4: Confirming placement option (Amazon Step 5)...");

            // Extract placementOptionId and confirm placement
            var placementOptionId = placementOptions.Payload.PlacementOptions.First().PlacementOptionId;
            _logger.LogInformation("STEP 4: Using placement option ID: '{PlacementOptionId}'", placementOptionId);

            var confirmPlacementResponse = await ConfirmPlacementOptionAsync(inboundPlanId, placementOptionId);

            if (!confirmPlacementResponse.IsSuccess)
            {
                _logger.LogWarning("STEP 4 FAILED: Failed to confirm placement option {PlacementOptionId}: {Errors}",
                    placementOptionId, string.Join(", ", confirmPlacementResponse.Errors.Select(e => e.Message)));
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Errors = confirmPlacementResponse.Errors
                };
            }
            _logger.LogInformation("STEP 4 SUCCESS: Placement option confirmed successfully");

            // Return successful response
            _logger.LogInformation("=== AMAZON COMPLIANT WORKFLOW COMPLETED SUCCESSFULLY ===");
            return new AmazonApiResponse<CreateInboundShipmentResponse>
            {
                Errors = new List<ApiError>(),
                Payload = new CreateInboundShipmentResponse
                {
                    InboundPlanId = inboundPlanId,
                    OperationId = "placement-confirmation-complete",
                    Shipments = new List<ShipmentSummary>
                    {
                        new ShipmentSummary
                        {
                            ShipmentId = inboundPlanId, // Use plan ID as shipment ID for now
                            Name = request.ShipmentName,
                            DestinationFulfillmentCenterId = request.DestinationFulfillmentCenterId,
                            ShipmentStatus = "WORKING"
                        }
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating inbound shipment for plan {InboundPlanId}", inboundPlanId);
            throw;
        }
    }

    /// <summary>
    /// Creates an inbound shipment using the Amazon documented workflow for India marketplace
    /// This follows the official Amazon documentation sequence: placement options → packing info → confirm placement
    /// Removes the incorrect packing options workflow and follows Amazon's documented steps
    /// </summary>
    public async Task<AmazonApiResponse<CreateInboundShipmentResponse>> CreateInboundShipmentForIndiaAsync(
        string inboundPlanId,
        CreateInboundShipmentRequest request)
    {
        try
        {
            _logger.LogInformation("=== CreateInboundShipmentForIndiaAsync - AMAZON COMPLIANT WORKFLOW ===");
            _logger.LogInformation("Starting Amazon-compliant inbound shipment creation for plan {InboundPlanId} with name {ShipmentName}",
                inboundPlanId, request.ShipmentName);
            _logger.LogInformation("Following Amazon's documented India marketplace workflow");

            // Declare shipmentId variable at method scope to ensure availability throughout the method
            string? actualShipmentId = null;

            // STEP 1: Generate placement options (Amazon Documentation Step 2)
            _logger.LogInformation("STEP 1: Generating placement options for India marketplace...");
            var generatePlacementResponse = await GeneratePlacementOptionsAsync(inboundPlanId,
                request.DestinationFulfillmentCenterId,
                request.Items?.Select(item => new ShipmentItem { Sku = item.Sku, Quantity = item.Quantity }).ToList());

            if (!generatePlacementResponse.IsSuccess)
            {
                _logger.LogWarning("STEP 1 FAILED: Failed to generate placement options for plan {InboundPlanId}: {Errors}",
                    inboundPlanId, string.Join(", ", generatePlacementResponse.Errors.Select(e => e.Message)));
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Errors = EnhanceWorkflowErrors(generatePlacementResponse.Errors, "placement options generation")
                };
            }
            _logger.LogInformation("STEP 1 SUCCESS: Placement options generated successfully");

            // STEP 1.1: Monitor placement options generation operation status
            string? placementOperationId = null;
            if (generatePlacementResponse.Payload != null)
            {
                try
                {
                    var placementResponseJson = System.Text.Json.JsonSerializer.Serialize(generatePlacementResponse.Payload);
                    var responseElement = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(placementResponseJson);
                    if (responseElement.TryGetProperty("operationId", out var operationIdElement))
                    {
                        placementOperationId = operationIdElement.GetString();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not extract operation ID from placement options response");
                }
            }

            if (!string.IsNullOrEmpty(placementOperationId))
            {
                var (pollSuccess, pollError) = await PollOperationStatusAsync(placementOperationId, "placement options generation");
                if (!pollSuccess)
                {
                    return new AmazonApiResponse<CreateInboundShipmentResponse>
                    {
                        Errors = new List<ApiError> { new() { Code = "PlacementOptionsOperationFailed", Message = pollError ?? "Placement options generation operation failed" } }
                    };
                }
            }

            // STEP 2: Monitor placement generation operation if async (simplified for now)
            _logger.LogInformation("STEP 2: Checking for operation ID in placement generation response...");
            // Using existing operation monitoring logic pattern

            // STEP 3: List placement options with retry logic
            _logger.LogInformation("STEP 3: Listing placement options with enhanced retry logic...");
            var placementOptions = await ListPlacementOptionsAsync(inboundPlanId, pageSize: 1);
            
            // Retry logic for placement options (existing pattern)
            var maxRetries = 5;
            var delaySeconds = new[] { 5, 10, 15, 20, 30 };
            
            for (int attempt = 0; attempt < maxRetries && (!placementOptions.IsSuccess || placementOptions.Payload?.PlacementOptions?.Any() != true); attempt++)
            {
                if (attempt > 0)
                {
                    _logger.LogInformation("STEP 3 RETRY {Attempt}/{MaxRetries}: Waiting {DelaySeconds} seconds before retry...", 
                        attempt + 1, maxRetries, delaySeconds[attempt - 1]);
                    await Task.Delay(delaySeconds[attempt - 1] * 1000);
                }
                
                _logger.LogInformation("STEP 3 ATTEMPT {Attempt}: Listing placement options...", attempt + 1);
                placementOptions = await ListPlacementOptionsAsync(inboundPlanId, pageSize: 1);
            }
            
            if (placementOptions == null || !placementOptions.IsSuccess || placementOptions.Payload?.PlacementOptions?.Any() != true)
            {
                _logger.LogWarning("STEP 3 FAILED: No placement options available for plan {InboundPlanId}", inboundPlanId);
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Errors = placementOptions?.Errors ?? new List<ApiError> { new() { Code = "NoPlacementOptionsAvailable", Message = "No placement options available after generation" } }
                };
            }
            _logger.LogInformation("STEP 3 SUCCESS: Found {Count} placement options", placementOptions.Payload.PlacementOptions.Count);

            // Extract shipmentId from placement options for later use
            actualShipmentId = placementOptions.Payload?.PlacementOptions?.FirstOrDefault()?.ShipmentIds?.FirstOrDefault();
            _logger.LogInformation("STEP 3: Extracted shipmentId from placement options: {ShipmentId}", actualShipmentId ?? "null (will use inboundPlanId as fallback)");

            // STEP 4: Set packing information BEFORE confirming placement (Amazon requirement for India)
            if (request.Boxes?.Any() == true)
            {
                _logger.LogInformation("STEP 4: Setting packing information (required for India marketplace)...");

                // Extract shipmentId from placement options response
                var shipmentId = placementOptions.Payload?.PlacementOptions?.FirstOrDefault()?.ShipmentIds?.FirstOrDefault();
                _logger.LogInformation("STEP 4: Using shipmentId from placement options: {ShipmentId}", shipmentId ?? "null (will use inboundPlanId)");

                var packingResponse = await UpdateInboundShipmentAsync(inboundPlanId, request, shipmentId);

                if (!packingResponse.IsSuccess)
                {
                    _logger.LogWarning("STEP 4 FAILED: Failed to set packing information for plan {InboundPlanId}: {Errors}",
                        inboundPlanId, string.Join(", ", packingResponse.Errors.Select(e => e.Message)));
                    return new AmazonApiResponse<CreateInboundShipmentResponse>
                    {
                        Errors = packingResponse.Errors
                    };
                }
                _logger.LogInformation("STEP 4 SUCCESS: Packing information set successfully");

                // STEP 4.1: Monitor packing information operation status if available
                string? packingOperationId = null;
                if (packingResponse.Payload != null)
                {
                    try
                    {
                        var packingResponseJson = System.Text.Json.JsonSerializer.Serialize(packingResponse.Payload);
                        var responseElement = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(packingResponseJson);
                        if (responseElement.TryGetProperty("operationId", out var operationIdElement))
                        {
                            packingOperationId = operationIdElement.GetString();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Could not extract operation ID from packing information response");
                    }
                }

                if (!string.IsNullOrEmpty(packingOperationId))
                {
                    var (pollSuccess, pollError) = await PollOperationStatusAsync(packingOperationId, "packing information update");
                    if (!pollSuccess)
                    {
                        return new AmazonApiResponse<CreateInboundShipmentResponse>
                        {
                            Errors = new List<ApiError> { new() { Code = "PackingInformationOperationFailed", Message = pollError ?? "Packing information operation failed" } }
                        };
                    }
                }
            }
            else
            {
                _logger.LogInformation("STEP 4 SKIPPED: No box information provided");
            }

            // STEP 5: Confirm placement option (Amazon Documentation Step 4)
            var placementOptionId = placementOptions.Payload.PlacementOptions.First().PlacementOptionId;
            _logger.LogInformation("STEP 5: Confirming placement option {PlacementOptionId}...", placementOptionId);

            // Use the existing method from the codebase
            var confirmPlacementResponse = await ConfirmPlacementOptionAsync(inboundPlanId, placementOptionId);

            if (!confirmPlacementResponse.IsSuccess)
            {
                _logger.LogWarning("STEP 5 FAILED: Failed to confirm placement option {PlacementOptionId}: {Errors}",
                    placementOptionId, string.Join(", ", confirmPlacementResponse.Errors.Select(e => e.Message)));
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Errors = confirmPlacementResponse.Errors
                };
            }
            _logger.LogInformation("STEP 5 SUCCESS: Placement option confirmed successfully");

            // STEP 5.1: Monitor placement confirmation operation status if available
            string? confirmOperationId = null;
            if (confirmPlacementResponse.Payload != null)
            {
                try
                {
                    var confirmResponseJson = System.Text.Json.JsonSerializer.Serialize(confirmPlacementResponse.Payload);
                    var responseElement = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(confirmResponseJson);
                    if (responseElement.TryGetProperty("operationId", out var operationIdElement))
                    {
                        confirmOperationId = operationIdElement.GetString();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not extract operation ID from placement confirmation response");
                }
            }

            if (!string.IsNullOrEmpty(confirmOperationId))
            {
                var (pollSuccess, pollError) = await PollOperationStatusAsync(confirmOperationId, "placement option confirmation");
                if (!pollSuccess)
                {
                    return new AmazonApiResponse<CreateInboundShipmentResponse>
                    {
                        Errors = new List<ApiError> { new() { Code = "PlacementConfirmationOperationFailed", Message = pollError ?? "Placement confirmation operation failed" } }
                    };
                }
            }

            // STEP 6: Verify shipment details are accessible with correct IDs and store details
            var finalShipmentId = actualShipmentId ?? inboundPlanId;
            _logger.LogInformation("STEP 6: Verifying shipment details accessibility with inboundPlanId: {InboundPlanId}, shipmentId: {ShipmentId}", inboundPlanId, finalShipmentId);

            object? shipmentDetails = null;
            try
            {
                var shipmentDetailsResponse = await GetShipmentDetailsAsync(inboundPlanId, finalShipmentId);
                if (shipmentDetailsResponse.IsSuccess)
                {
                    _logger.LogInformation("STEP 6 SUCCESS: Shipment details verified successfully");
                    shipmentDetails = shipmentDetailsResponse.Payload; // Store for future use
                }
                else
                {
                    _logger.LogWarning("STEP 6 WARNING: Could not verify shipment details: {Errors}",
                        string.Join(", ", shipmentDetailsResponse.Errors?.Select(e => e.Message) ?? new List<string>()));
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "STEP 6 WARNING: Exception while verifying shipment details");
            }

            // STEP 7: Generate transportation options (Amazon Documentation Step 5)
            _logger.LogInformation("STEP 7: Generating transportation options for inbound plan {InboundPlanId}...", inboundPlanId);
            var generateTransportResponse = await GenerateTransportationOptionsAsync(inboundPlanId, placementOptionId, finalShipmentId);

            if (!generateTransportResponse.IsSuccess)
            {
                _logger.LogWarning("STEP 7 FAILED: Failed to generate transportation options: {Errors}",
                    string.Join(", ", generateTransportResponse.Errors.Select(e => e.Message)));
                // Continue workflow - transportation is optional for some shipment types
            }
            else
            {
                _logger.LogInformation("STEP 7 SUCCESS: Transportation options generated successfully");

                // STEP 8: List transportation options to get available options
                _logger.LogInformation("STEP 8: Listing transportation options for inbound plan {InboundPlanId}...", inboundPlanId);
                var listTransportResponse = await ListTransportationOptionsAsync(inboundPlanId, placementOptionId);

                if (!listTransportResponse.IsSuccess)
                {
                    _logger.LogWarning("STEP 8 FAILED: Failed to list transportation options: {Errors}",
                        string.Join(", ", listTransportResponse.Errors.Select(e => e.Message)));
                    // Continue workflow - transportation is optional for some shipment types
                }
                else
                {
                    _logger.LogInformation("STEP 8 SUCCESS: Transportation options listed successfully");

                    // Deserialize and store transportation options for future use
                    if (listTransportResponse.Payload?.TransportationOptions?.Any() == true)
                    {
                        _logger.LogInformation("STEP 8 SUCCESS: Found {Count} transportation options",
                            listTransportResponse.Payload.TransportationOptions.Count);

                        foreach (var option in listTransportResponse.Payload.TransportationOptions)
                        {
                            _logger.LogInformation("Transportation Option: {Id} - {Carrier} {Service} - Cost: {Cost} {Currency} - Amazon Partnered: {IsPartnered}",
                                option.TransportationOptionId, option.CarrierName, option.ServiceName,
                                option.Cost, option.Currency, option.IsAmazonPartnered);
                        }

                        // Store for future use (could be added to response payload)
                        var storedTransportationOptions = listTransportResponse.Payload.TransportationOptions;
                        _logger.LogInformation("Transportation options stored successfully for future confirmation");
                    }
                    else
                    {
                        _logger.LogWarning("STEP 8 WARNING: No transportation options found in response");
                    }

                    // Note: In a real implementation, you would present these options to the user to choose from
                    // For now, we'll log that transportation options are available but not auto-confirm
                    _logger.LogInformation("Transportation options are available for selection. Manual confirmation required.");
                }
            }

            // Return successful response with correct shipmentId and stored details
            _logger.LogInformation("=== AMAZON COMPLIANT WORKFLOW COMPLETED SUCCESSFULLY ===");
            _logger.LogInformation("Final shipmentId for response: {ShipmentId}", finalShipmentId);
            _logger.LogInformation("Shipment details stored: {HasDetails}", shipmentDetails != null ? "Yes" : "No");

            return new AmazonApiResponse<CreateInboundShipmentResponse>
            {
                Errors = new List<ApiError>(),
                Payload = new CreateInboundShipmentResponse
                {
                    InboundPlanId = inboundPlanId,
                    OperationId = "amazon-compliant-workflow-complete",
                    Shipments = new List<ShipmentSummary>
                    {
                        new ShipmentSummary
                        {
                            ShipmentId = finalShipmentId, // Use the verified shipmentId
                            Name = request.ShipmentName,
                            DestinationFulfillmentCenterId = request.DestinationFulfillmentCenterId,
                            ShipmentStatus = "WORKING"
                        }
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Amazon compliant workflow for plan {InboundPlanId}", inboundPlanId);
            throw;
        }
    }

    private async Task<AmazonApiResponse<CreateInboundShipmentResponse>> CompleteIndiaWorkflowSteps(
        string inboundPlanId, 
        CreateInboundShipmentRequest request, 
        AmazonApiResponse<object> generatePlacementResponse)
    {
        // Implementation continues here...
        // This is a placeholder for the remaining workflow steps
        return new AmazonApiResponse<CreateInboundShipmentResponse>
        {
            Errors = new List<ApiError>(),
            Payload = new CreateInboundShipmentResponse
            {
                InboundPlanId = inboundPlanId,
                OperationId = "placement-confirmation-complete",
                Shipments = new List<ShipmentSummary>()
            }
        };
    }




    /// <summary>
    /// Updates inbound shipment packing information using v2024-03-20 API
    /// This replaces the old updateInboundShipment operation
    /// </summary>
    public async Task<AmazonApiResponse<object>> UpdateInboundShipmentAsync(
        string inboundPlanId,
        CreateInboundShipmentRequest request,
        string? shipmentId = null)
    {
        try
        {
            // Debug logging for method entry and parameters
            _logger.LogInformation("=== UpdateInboundShipmentAsync DEBUG ===");
            _logger.LogInformation("Method Entry - Inbound Plan ID: {InboundPlanId}", inboundPlanId);
            _logger.LogInformation("Provided Shipment ID: {ShipmentId}", shipmentId ?? "null (will use inboundPlanId)");
            _logger.LogInformation("Request Details: {Request}", System.Text.Json.JsonSerializer.Serialize(request));
            _logger.LogInformation("Boxes Count: {BoxCount}", request.Boxes?.Count ?? 0);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingInformation";

            // Create corrected payload structure with single packageGroupings entry containing all boxes
            var requestPayload = new
            {
                packageGroupings = new[]
                {
                    new
                    {
                        //packingGroupId = $"SHIPMENT-GROUP-{DateTime.UtcNow:yyyyMMddHHmmss}",
                        shipmentId = shipmentId ?? inboundPlanId, // Use provided shipmentId or fallback to inboundPlanId
                        boxes = (request.Boxes ?? new List<ShipmentBox>()).Select(box => new
                        {
                            contentInformationSource = "BOX_CONTENT_PROVIDED",
                            weight = new
                            {
                                unit = box.Weight.Unit.ToUpper(),
                                value = box.Weight.Value
                            },
                            dimensions = new
                            {
                                length = box.Dimensions.Length,
                                width = box.Dimensions.Width,
                                height = box.Dimensions.Height,
                                unitOfMeasurement = box.Dimensions.Unit.ToUpper()
                            },
                            items = (box.Contents ?? new List<BoxContent>()).Select(item => new
                            {
                                msku = item.Sku,
                                prepOwner = "NONE", // Required field per user preferences
                                labelOwner = "SELLER", // Required field per codebase patterns
                                quantity = item.Quantity
                            }).ToList(),
                            quantity = 1
                        }).ToList()
                    }
                }
            };

            _logger.LogInformation("Packing Information Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(requestPayload));

            // Use specialized method to extract operationId from response
            var (result, operationId) = await _apiClient.PostWithOperationIdAsync(endpoint, requestPayload);

            _logger.LogInformation("=== UpdateInboundShipmentAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            // CRITICAL: Poll operation status if operationId is returned (required for v2024-03-20 API)
            if (result.IsSuccess)
            {
                if (!string.IsNullOrEmpty(operationId))
                {
                    _logger.LogInformation("Packing information returned operation ID: {OperationId}, polling for completion...", operationId);

                    var (pollSuccess, pollError) = await PollOperationStatusAsync(operationId, "packing information update");
                    if (!pollSuccess)
                    {
                        _logger.LogError("Packing information operation failed: {Error}", pollError);
                        return new AmazonApiResponse<object>
                        {
                            Errors = new List<ApiError> { new() { Code = "PackingInformationOperationFailed", Message = pollError ?? "Packing information operation failed" } }
                        };
                    }
                    _logger.LogInformation("Packing information operation completed successfully");
                }
                else
                {
                    _logger.LogInformation("No operation ID returned from packing information API - operation completed synchronously");
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting packing information for plan {InboundPlanId}", inboundPlanId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<GetShipmentResponse>> GetInboundShipmentAsync(string inboundPlanId, string shipmentId)
    {
        try
        {
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}";

            _logger.LogInformation("Getting inbound shipment details for {ShipmentId}", shipmentId);

            return await _apiClient.GetAsync<GetShipmentResponse>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inbound shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<List<GetShipmentResponse>>> GetInboundShipmentsAsync(
        List<string>? shipmentStatusList = null,
        List<string>? shipmentIdList = null,
        DateTime? lastUpdatedAfter = null,
        DateTime? lastUpdatedBefore = null,
        string inboundPlanId = "")
    {
        try
        {
            var endpoint = "/inbound/fba/2024-03-20/inboundPlans/"+inboundPlanId+"/shipments";
            var queryParams = new Dictionary<string, string>
            {
                ["MarketplaceId"] = _credentials.MarketplaceId
            };

            if (shipmentStatusList?.Any() == true)
                queryParams["ShipmentStatusList"] = string.Join(",", shipmentStatusList);

            if (shipmentIdList?.Any() == true)
                queryParams["ShipmentIdList"] = string.Join(",", shipmentIdList);

            if (lastUpdatedAfter.HasValue)
                queryParams["LastUpdatedAfter"] = lastUpdatedAfter.Value.ToString("yyyy-MM-ddTHH:mm:ssZ");

            if (lastUpdatedBefore.HasValue)
                queryParams["LastUpdatedBefore"] = lastUpdatedBefore.Value.ToString("yyyy-MM-ddTHH:mm:ssZ");

            _logger.LogInformation("Getting inbound shipments list");

            // First try with the wrapped response format
            var wrappedResponse = await _apiClient.GetAsync<ShipmentsListResponse>(endpoint, queryParams);
            
            if (wrappedResponse.IsSuccess && wrappedResponse.Payload != null)
            {
                return new AmazonApiResponse<List<GetShipmentResponse>>
                {
                    Payload = wrappedResponse.Payload.ShipmentData,
                    Errors = wrappedResponse.Errors
                };
            }
            
            // If that fails, try with direct array response
            var directResponse = await _apiClient.GetAsync<List<GetShipmentResponse>>(endpoint, queryParams);
            
            if (directResponse.IsSuccess && directResponse.Payload != null)
            {
                return new AmazonApiResponse<List<GetShipmentResponse>>
                {
                    Payload = directResponse.Payload,
                    Errors = directResponse.Errors
                };
            }
            
            // Return the error from the wrapped response attempt
            return new AmazonApiResponse<List<GetShipmentResponse>>
            {
                Payload = new List<GetShipmentResponse>(),
                Errors = wrappedResponse.Errors.Count != 0 ? wrappedResponse.Errors : directResponse.Errors
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inbound shipments list");
            throw;
        }
    }

    /// <summary>
    /// Generate transportation options using v2024-03-20 API
    /// This replaces the old putTransportDetails + estimateTransport operations
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="placementOptionId">Placement option ID from previous step</param>
    /// <param name="shipmentId">Shipment ID from previous step</param>
    /// <returns>Transportation options response with operationId</returns>
    public async Task<AmazonApiResponse<object>> GenerateTransportationOptionsAsync(string inboundPlanId, string placementOptionId, string shipmentId)
    {
        try
        {
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions";

            _logger.LogInformation("Generating transportation options for inbound plan {InboundPlanId}, placement option {PlacementOptionId}, shipment {ShipmentId}",
                inboundPlanId, placementOptionId, shipmentId);

            // Build the request payload with all required fields
            var request = new GenerateTransportationOptionsRequest
            {
                PlacementOptionId = placementOptionId,
                ShipmentTransportationConfigurations = new List<ShipmentTransportationConfiguration>
                {
                    new ShipmentTransportationConfiguration
                    {
                        ShipmentId = shipmentId,
                        ContactInformation = new ContactInformation
                        {
                            Email = ShipmentConstants.Transportation.ContactInformation.Email,
                            Name = ShipmentConstants.Transportation.ContactInformation.Name,
                            PhoneNumber = ShipmentConstants.Transportation.ContactInformation.PhoneNumber
                        },
                        ReadyToShipWindow = new ReadyToShipWindow
                        {
                            Start = DateTime.UtcNow.AddDays(ShipmentConstants.Transportation.ReadyToShipWindow.StartDateOffsetDays)
                        },
                        Pallets = new List<PalletInput>
                        {
                            new PalletInput
                            {
                                Quantity = 1,
                                Stackability = "NON_STACKABLE"
                            }
                        },
                        FreightInformation = new FreightInformation
                        {
                            FreightClass = "FC_50",
                            DeclaredValue = new DeclaredValue
                            {
                                Amount = "50000",
                                Code = "INR"
                            }
                        }
                    }
                }
            };

            _logger.LogInformation("Transportation request payload: PlacementOptionId={PlacementOptionId}, ShipmentId={ShipmentId}, Email={Email}, ReadyToShipStart={Start}",
                request.PlacementOptionId, request.ShipmentTransportationConfigurations[0].ShipmentId,
                request.ShipmentTransportationConfigurations[0].ContactInformation.Email,
                request.ShipmentTransportationConfigurations[0].ReadyToShipWindow.Start);

            // Use specialized method to extract operationId from response
            var (result, operationId) = await _apiClient.PostWithOperationIdAsync(endpoint, request);

            if (result.IsSuccess && !string.IsNullOrEmpty(operationId))
            {
                // Poll for operation completion before proceeding
                _logger.LogInformation("POLLING: Starting operation status polling for operationId: {OperationId}", operationId);
                var (pollSuccess, pollError) = await PollOperationStatusAsync(operationId, "transportation options generation");
                if (!pollSuccess)
                {
                    _logger.LogWarning("Transportation options generation operation failed: {Error}", pollError);
                    return new AmazonApiResponse<object>
                    {
                        Errors = new List<ApiError> { new() { Code = "TransportationGenerationOperationFailed", Message = pollError ?? "Transportation options generation operation failed" } }
                    };
                }
                _logger.LogInformation("POLLING: Transportation options generation operation completed successfully");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating transportation options for plan {InboundPlanId}", inboundPlanId);
            throw;
        }
    }

    /// <summary>
    /// List available transportation options using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="placementOptionId">Placement option ID for filtering</param>
    /// <returns>Transportation options list</returns>
    public async Task<AmazonApiResponse<TransportationOptionsResponse>> ListTransportationOptionsAsync(string inboundPlanId, string placementOptionId)
    {
        try
        {
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions?placementOptionId={placementOptionId}";

            _logger.LogInformation("Listing transportation options for inbound plan {InboundPlanId} with placement option {PlacementOptionId}",
                inboundPlanId, placementOptionId);

            return await _apiClient.GetAsync<TransportationOptionsResponse>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listing transportation options for plan {InboundPlanId}", inboundPlanId);
            throw;
        }
    }

    /// <summary>
    /// Confirm transportation options using v2024-03-20 API
    /// This replaces the old confirmTransport operation
    /// </summary>
    public async Task<AmazonApiResponse<object>> ConfirmTransportationOptionsAsync(string inboundPlanId, string transportationOptionId)
    {
        try
        {
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions/{transportationOptionId}/confirmation";

            _logger.LogInformation("Confirming transportation option {TransportationOptionId} for plan {InboundPlanId}",
                transportationOptionId, inboundPlanId);

            return await _apiClient.PostAsync<object>(endpoint, new { });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming transportation option {TransportationOptionId} for plan {InboundPlanId}",
                transportationOptionId, inboundPlanId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<object>> GetLabelsAsync(
        string shipmentId,
        string pageType = "PackageLabel_Letter_6",
        string labelType = "UNIQUE",
        int? numberOfPackages = null)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}/labels";
            var queryParams = new Dictionary<string, string>
            {
                ["PageType"] = pageType,
                ["LabelType"] = labelType
            };

            if (numberOfPackages.HasValue)
                queryParams["NumberOfPackages"] = numberOfPackages.Value.ToString();

            _logger.LogInformation("Getting labels for shipment {ShipmentId}", shipmentId);

            return await _apiClient.GetAsync<object>(endpoint, queryParams);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting labels for shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<object>> GetBillOfLadingAsync(string shipmentId)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}/billOfLading";
            
            _logger.LogInformation("Getting bill of lading for shipment {ShipmentId}", shipmentId);

            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting bill of lading for shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<bool> ValidateCredentialsAsync()
    {
        try
        {
            _logger.LogInformation("=== ValidateCredentialsAsync DEBUG ===");
            _logger.LogInformation("Starting credential validation using preserved v0 endpoint");
            _logger.LogInformation("Marketplace ID: {MarketplaceId}", _credentials.MarketplaceId);
            _logger.LogInformation("Base URL: {BaseUrl}", _credentials.BaseUrl);

            // Use the preserved v0 endpoint for credential validation
            // This endpoint is still available and works for authentication testing
            var endpoint = "/fba/inbound/v0/shipments";
            var queryParams = new Dictionary<string, string>
            {
                ["MarketplaceId"] = _credentials.MarketplaceId
            };

            _logger.LogInformation("Validation endpoint: {Endpoint}", endpoint);
            _logger.LogInformation("Query parameters: {QueryParams}", System.Text.Json.JsonSerializer.Serialize(queryParams));

            var response = await _apiClient.GetAsync<List<GetShipmentResponse>>(endpoint, queryParams);

            var isValid = response.IsSuccess ||
                         (response.Errors.Count != 0 && !response.Errors.Any(e =>
                             e.Code.Contains("Unauthorized") ||
                             e.Code.Contains("InvalidAccessToken") ||
                             e.Code.Contains("AccessDenied")));

            _logger.LogInformation("=== ValidateCredentialsAsync RESULT ===");
            _logger.LogInformation("Credential validation result: {IsValid}", isValid);
            _logger.LogInformation("Response success: {Success}", response.IsSuccess);
            _logger.LogInformation("Error count: {ErrorCount}", response.Errors?.Count ?? 0);
            if (response.Errors?.Any() == true)
            {
                _logger.LogInformation("Validation errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(response.Errors));
            }

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating credentials");
            return false;
        }
    }

    #region New v2024-03-20 API Helper Methods

    /// <summary>
    /// Generate packing options for an inbound plan
    /// </summary>
    private async Task<AmazonApiResponse<object>> GeneratePackingOptionsAsync(string inboundPlanId)
    {
        try
        {
            _logger.LogInformation("=== GeneratePackingOptionsAsync DEBUG ===");
            _logger.LogInformation("Method Entry - Inbound Plan ID: {InboundPlanId}", inboundPlanId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions";
            
            var result = await _apiClient.PostAsync<object>(endpoint, new { });

            _logger.LogInformation("=== GeneratePackingOptionsAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating packing options for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// List available packing options for an inbound plan
    /// </summary>
    private async Task<AmazonApiResponse<PackingOptionsResponse>> ListPackingOptionsAsync(string inboundPlanId)
    {
        try
        {
            _logger.LogInformation("=== ListPackingOptionsAsync DEBUG ===");
            _logger.LogInformation("Method Entry - Inbound Plan ID: {InboundPlanId}", inboundPlanId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions";
            
            var result = await _apiClient.GetAsync<PackingOptionsResponse>(endpoint);

            _logger.LogInformation("=== ListPackingOptionsAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listing packing options for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<PackingOptionsResponse>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Confirm a packing option for an inbound plan
    /// </summary>
    private async Task<AmazonApiResponse<object>> ConfirmPackingOptionAsync(string inboundPlanId, string packingOptionId)
    {
        try
        {
            _logger.LogInformation("=== ConfirmPackingOptionAsync DEBUG ===");
            _logger.LogInformation("Method Entry - Inbound Plan ID: {InboundPlanId}", inboundPlanId);
            _logger.LogInformation("Packing Option ID: {PackingOptionId}", packingOptionId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions/{packingOptionId}/confirmation";
            
            var result = await _apiClient.PostAsync<object>(endpoint, new { });

            _logger.LogInformation("=== ConfirmPackingOptionAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming packing option {PackingOptionId} for plan {InboundPlanId}", packingOptionId, inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Confirm placement option for an inbound plan
    /// </summary>
    private async Task<AmazonApiResponse<CreateInboundShipmentResponse>> ConfirmPlacementOptionAsync(string inboundPlanId, string packingOptionId)
    {
        try
        {
            _logger.LogInformation("=== ConfirmPlacementOptionAsync DEBUG ===");
            _logger.LogInformation("Method Entry - Inbound Plan ID: {InboundPlanId}", inboundPlanId);
            _logger.LogInformation("Packing Option ID (used as placement option): {PackingOptionId}", packingOptionId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions/{packingOptionId}/confirmation";

            // Use specialized method to extract operationId from response
            var (result, operationId) = await _apiClient.PostWithOperationIdAsync(endpoint, new { });

            _logger.LogInformation("=== ConfirmPlacementOptionAsync RESULT ===");
            _logger.LogInformation("Success: {Success}", result.IsSuccess);
            _logger.LogInformation("Errors Count: {ErrorCount}", result.Errors?.Count ?? 0);
            _logger.LogInformation("OperationId: {OperationId}", operationId ?? "null");
            if (result.Errors?.Any() == true)
            {
                _logger.LogInformation("Errors: {Errors}", System.Text.Json.JsonSerializer.Serialize(result.Errors));
            }
            if (result.Payload != null)
            {
                _logger.LogInformation("Response Payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(result.Payload));
            }

            if (result.IsSuccess && !string.IsNullOrEmpty(operationId))
            {
                // Poll for operation completion before proceeding
                _logger.LogInformation("POLLING: Starting operation status polling for operationId: {OperationId}", operationId);
                var (pollSuccess, pollError) = await PollOperationStatusAsync(operationId, "placement option confirmation");
                if (!pollSuccess)
                {
                    _logger.LogWarning("STEP 5 FAILED: Placement option confirmation operation failed: {Error}", pollError);
                    return new AmazonApiResponse<CreateInboundShipmentResponse>
                    {
                        Errors = new List<ApiError> { new() { Code = "PlacementConfirmationOperationFailed", Message = pollError ?? "Placement option confirmation operation failed" } }
                    };
                }

                _logger.LogInformation("STEP 5 SUCCESS: Placement option confirmed successfully after polling");
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Payload = new CreateInboundShipmentResponse(),
                    Errors = new List<ApiError>()
                };
            }
            else if (result.IsSuccess)
            {
                // Success but no operationId - immediate success
                _logger.LogInformation("STEP 5 SUCCESS: Placement option confirmed successfully (no polling required)");
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Payload = new CreateInboundShipmentResponse(),
                    Errors = new List<ApiError>()
                };
            }
            else
            {
                _logger.LogWarning("STEP 5 FAILED: Failed to confirm placement option: {Errors}",
                    string.Join(", ", result.Errors?.Select(e => e.Message) ?? new List<string>()));
                return new AmazonApiResponse<CreateInboundShipmentResponse>
                {
                    Errors = result.Errors ?? new List<ApiError>()
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming placement option for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<CreateInboundShipmentResponse>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Enhances workflow-specific errors for better user understanding
    /// </summary>
    private static List<ApiError> EnhanceWorkflowErrors(List<ApiError> originalErrors, string workflowStep)
    {
        var enhancedErrors = new List<ApiError>();

        foreach (var error in originalErrors)
        {
            var enhancedError = new ApiError
            {
                Code = error.Code ?? "UNKNOWN_ERROR",
                Details = error.Details
            };

            // Enhance error messages based on workflow step and error code
            enhancedError.Message = (error.Code?.ToUpper(), workflowStep) switch
            {
                ("INVALID_ARGUMENT", "packing options generation") =>
                    "Invalid shipment data for packing options. Please check item quantities and dimensions.",
                ("RESOURCE_NOT_FOUND", _) =>
                    $"Inbound plan not found during {workflowStep}. The plan may have expired or been deleted.",
                ("PERMISSION_DENIED", _) =>
                    $"Access denied during {workflowStep}. Please check your API permissions.",
                ("QUOTA_EXCEEDED", _) =>
                    $"Rate limit exceeded during {workflowStep}. Please wait before retrying.",
                ("INTERNAL_SERVER_ERROR", _) =>
                    $"Amazon's servers encountered an error during {workflowStep}. Please try again later.",
                ("DEPRECATED_API", _) =>
                    "This API version is deprecated. The application needs to be updated to use the latest API version.",
                _ => string.IsNullOrEmpty(error.Message) ?
                    $"Error during {workflowStep}: {error.Code}" :
                    $"{error.Message} (during {workflowStep})"
            };

            enhancedErrors.Add(enhancedError);
        }

        return enhancedErrors;
    }

    #endregion

    /// <summary>
    /// Generate placement options for destination fulfillment centers
    /// Required for India marketplace workflow (Step 2)
    /// For India marketplace, this is where we specify the destination fulfillment center and items using customPlacement
    /// </summary>
    public async Task<AmazonApiResponse<object>> GeneratePlacementOptionsAsync(string inboundPlanId, string? destinationFulfillmentCenterId = null, List<ShipmentItem>? items = null)
    {
        try
        {
            _logger.LogInformation("Generating placement options for inbound plan {InboundPlanId} with destination FC {DestinationFC}",
                inboundPlanId, destinationFulfillmentCenterId);

            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions";

            // For India marketplace, use customPlacement in the request body instead of query parameter
            object requestPayload;
            if (!string.IsNullOrEmpty(destinationFulfillmentCenterId) && items != null && items.Any())
            {
                _logger.LogInformation("Using customPlacement for India marketplace with destination FC: {DestinationFC}", destinationFulfillmentCenterId);

                // Create custom placement for India marketplace
                // Note: customPlacement should be an array with warehouseId (not fulfillmentCenterId)
                var customPlacement = new
                {
                    customPlacement = new[]
                    {
                        new
                        {
                            warehouseId = destinationFulfillmentCenterId,
                            items = items.Select(item => new
                            {
                                msku = item.Sku,
                                quantity = item.Quantity,
                                labelOwner = "SELLER",  // Required: AMAZON, NONE, or SELLER
                                prepOwner = "NONE"      // Required: AMAZON, NONE, or SELLER
                            }).ToList()
                        }
                    }
                };

                requestPayload = customPlacement;
                _logger.LogInformation("Custom placement payload: {Payload}", System.Text.Json.JsonSerializer.Serialize(customPlacement));
                _logger.LogInformation("IMPORTANT: Using warehouseId '{WarehouseId}' for India marketplace. Ensure this is a valid fulfillment center ID for your account.", destinationFulfillmentCenterId);
            }
            else
            {
                // For other marketplaces or when no specific FC is provided, use empty body
                requestPayload = new { };
                _logger.LogInformation("Using empty payload for standard placement options generation");
            }

            // Use PostDirectAsync to get the direct response for placement generation
            return await _apiClient.PostDirectAsync<object>(endpoint, requestPayload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating placement options for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// List placement options for destination fulfillment centers
    /// Required for India marketplace workflow (Step 2)
    /// CRITICAL FIX: Amazon returns placement options data directly (not wrapped in payload)
    /// </summary>
    public async Task<AmazonApiResponse<PlacementOptionsResponse>> ListPlacementOptionsAsync(string inboundPlanId, int pageSize = 1, string? nextToken = null)
    {
        try
        {
            _logger.LogInformation("=== LIST PLACEMENT OPTIONS DEBUG ===");
            _logger.LogInformation("Listing placement options for inbound plan {InboundPlanId} with pageSize={PageSize}",
                inboundPlanId, pageSize);

            // CRITICAL FIX: Amazon returns placement options data directly (not wrapped in payload)
            // Use GetDirectAsync like we do for operation status API
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions";

            // Add query parameters if provided
            var queryParams = new Dictionary<string, string>();
            if (pageSize > 0)
            {
                queryParams["pageSize"] = pageSize.ToString();
            }
            if (!string.IsNullOrEmpty(nextToken))
            {
                queryParams["nextToken"] = nextToken;
            }

            _logger.LogInformation("Making placement options API call with endpoint: {Endpoint}", endpoint);
            _logger.LogInformation("Query parameters: {QueryParams}",
                queryParams.Any() ? string.Join(", ", queryParams.Select(kv => $"{kv.Key}={kv.Value}")) : "None");

            // CRITICAL FIX: Use GetDirectAsync because Amazon returns data directly, not wrapped in payload
            var response = await _apiClient.GetDirectAsync<PlacementOptionsResponse>(endpoint, queryParams.Any() ? queryParams : null);

            if (response.IsSuccess && response.Payload?.PlacementOptions?.Any() == true)
            {
                var placementOptionId = response.Payload.PlacementOptions.First().PlacementOptionId;
                _logger.LogInformation("SUCCESS: Found {Count} placement options. First placement option ID: {PlacementOptionId}",
                    response.Payload.PlacementOptions.Count, placementOptionId);

                // Store the placement option ID for future use
                _logger.LogInformation("CRITICAL: Placement Option ID extracted: {PlacementOptionId}", placementOptionId);
            }
            else if (response.IsSuccess)
            {
                _logger.LogInformation("API call successful but no placement options found");
            }
            else
            {
                _logger.LogWarning("API call failed. Errors: {Errors}",
                    string.Join(", ", response.Errors?.Select(e => e.Message) ?? new List<string>()));
            }

            // Enhanced debug logging for placement options response
            _logger.LogInformation("=== PLACEMENT OPTIONS RESPONSE DEBUG ===");
            _logger.LogInformation("Response Success: {IsSuccess}", response.IsSuccess);
            _logger.LogInformation("Response Errors Count: {ErrorCount}", response.Errors?.Count ?? 0);

            if (response.Errors?.Any() == true)
            {
                _logger.LogInformation("Response Errors: {Errors}",
                    System.Text.Json.JsonSerializer.Serialize(response.Errors));
            }

            if (response.Payload != null)
            {
                _logger.LogInformation("Placement Options Count: {Count}", response.Payload.PlacementOptions?.Count ?? 0);
                _logger.LogInformation("Operation ID: {OperationId}", response.Payload.OperationId ?? "None");

                if (response.Payload.PlacementOptions?.Any() == true)
                {
                    _logger.LogInformation("=== PLACEMENT OPTIONS DETAILS ===");
                    for (int i = 0; i < response.Payload.PlacementOptions.Count; i++)
                    {
                        var option = response.Payload.PlacementOptions[i];
                        _logger.LogInformation("Option {Index}: ID={PlacementOptionId}, Status={Status}, ShipmentIds={ShipmentIds}",
                            i + 1, option.PlacementOptionId, option.Status,
                            string.Join(",", option.ShipmentIds ?? new List<string>()));

                        if (option.Fees?.Any() == true)
                        {
                            _logger.LogInformation("  Fees: {Fees}",
                                string.Join(", ", option.Fees.Select(f => $"{f.Value?.Code} {f.Value?.Amount} ({f.Type})")));
                        }

                        if (option.Discounts?.Any() == true)
                        {
                            _logger.LogInformation("  Discounts: {DiscountCount} items", option.Discounts.Count);
                        }

                        if (!string.IsNullOrEmpty(option.Expiration))
                        {
                            _logger.LogInformation("  Expiration: {Expiration}", option.Expiration);
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("No placement options found in response payload");
                }

                // Log the complete response payload as JSON for debugging
                _logger.LogInformation("Complete Placement Options Response: {ResponsePayload}",
                    System.Text.Json.JsonSerializer.Serialize(response.Payload, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }));
            }
            else
            {
                _logger.LogWarning("Response payload is null");
            }

            _logger.LogInformation("=== END PLACEMENT OPTIONS DEBUG ===");

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listing placement options for plan {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<PlacementOptionsResponse>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Get inbound operation status (used to check async operation status)
    /// Required for monitoring Amazon API operations as per v2024-03-20 documentation
    /// CRITICAL: Operation status API returns data directly, not wrapped in payload
    /// </summary>
    public async Task<AmazonApiResponse<OperationStatusResponse>> GetInboundOperationStatusAsync(string operationId)
    {
        try
        {
            _logger.LogInformation("Getting operation status for operation {OperationId}", operationId);

            var endpoint = $"/inbound/fba/2024-03-20/operations/{operationId}";

            // CRITICAL FIX: Use direct deserialization for operation status API
            // This API returns data directly, not wrapped in the standard Amazon API response format
            var response = await _apiClient.GetDirectAsync<OperationStatusResponse>(endpoint);

            if (response.IsSuccess && response.Payload != null)
            {
                _logger.LogInformation("Operation status details - Status: '{Status}', OperationId: '{OperationId}', Operation: '{Operation}'",
                    response.Payload.OperationStatus, response.Payload.OperationId, response.Payload.Operation);

                if (response.Payload.OperationProblems?.Any() == true)
                {
                    _logger.LogWarning("Operation has {ProblemCount} problems: {Problems}",
                        response.Payload.OperationProblems.Count,
                        string.Join(", ", response.Payload.OperationProblems.Select(p => $"{p.Code}: {p.Message}")));
                }
            }
            else
            {
                _logger.LogWarning("Failed to get operation status for {OperationId}: {Errors}",
                    operationId, string.Join(", ", response.Errors?.Select(e => e.Message) ?? new List<string>()));
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting operation status for {OperationId}", operationId);
            return new AmazonApiResponse<OperationStatusResponse>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Set packing information for an inbound plan (India marketplace Step 4)
    /// </summary>
    public async Task<AmazonApiResponse<object>> SetPackingInformationAsync(string inboundPlanId, object packingInformation)
    {
        try
        {
            _logger.LogInformation("Setting packing information for plan {InboundPlanId}", inboundPlanId);
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingInformation";
            return await _apiClient.PutAsync<object>(endpoint, packingInformation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting packing information for {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Confirm placement option for India plan (India marketplace Step 5)
    /// </summary>
    public async Task<AmazonApiResponse<object>> ConfirmPlacementOptionForIndiaPlanAsync(string inboundPlanId, string placementOptionId)
    {
        try
        {
            _logger.LogInformation("Confirming placement option {PlacementOptionId} for plan {InboundPlanId}", placementOptionId, inboundPlanId);
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions/{placementOptionId}/confirmation";
            return await _apiClient.PostAsync<object>(endpoint, new { });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming placement option for {InboundPlanId}", inboundPlanId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Get shipment details using v2024-03-20 API
    /// </summary>
    public async Task<AmazonApiResponse<object>> GetShipmentDetailsAsync(string inboundPlanId, string shipmentId)
    {
        try
        {
            _logger.LogInformation("Getting shipment details for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}";
            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipment details for {InboundPlanId}/{ShipmentId}", inboundPlanId, shipmentId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Generate self-ship appointment slots (Step 7 - self-ship only)
    /// </summary>
    public async Task<AmazonApiResponse<object>> GenerateSelfShipAppointmentSlotsAsync(string inboundPlanId, string shipmentId, DateTime? startDate = null, DateTime? endDate = null)
    {
        try
        {
            _logger.LogInformation("Generating self-ship appointment slots for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentSlots";
            var requestBody = new
            {
                startDate = startDate?.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                endDate = endDate?.ToString("yyyy-MM-ddTHH:mm:ssZ")
            };
            return await _apiClient.PostAsync<object>(endpoint, requestBody);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating self-ship appointment slots for {InboundPlanId}/{ShipmentId}", inboundPlanId, shipmentId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Get available self-ship appointment slots (Step 8 - self-ship only)
    /// </summary>
    public async Task<AmazonApiResponse<object>> GetSelfShipAppointmentSlotsAsync(string inboundPlanId, string shipmentId)
    {
        try
        {
            _logger.LogInformation("Getting self-ship appointment slots for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentSlots";
            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting self-ship appointment slots for {InboundPlanId}/{ShipmentId}", inboundPlanId, shipmentId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Schedule a self-ship appointment slot (Step 8 - self-ship only)
    /// </summary>
    public async Task<AmazonApiResponse<object>> ScheduleSelfShipAppointmentAsync(string inboundPlanId, string shipmentId, string slotId, string? reasonComment = null)
    {
        try
        {
            _logger.LogInformation("Scheduling self-ship appointment slot {SlotId} for plan {InboundPlanId}, shipment {ShipmentId}", slotId, inboundPlanId, shipmentId);
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentSlots/{slotId}/schedule";
            var requestBody = new { reasonComment };
            return await _apiClient.PostAsync<object>(endpoint, requestBody);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling self-ship appointment slot for {InboundPlanId}/{ShipmentId}/{SlotId}", inboundPlanId, shipmentId, slotId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Cancel a self-ship appointment slot
    /// </summary>
    public async Task<AmazonApiResponse<object>> CancelSelfShipAppointmentAsync(string inboundPlanId, string shipmentId)
    {
        try
        {
            _logger.LogInformation("Cancelling self-ship appointment for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentSlots/cancellation";
            return await _apiClient.PostAsync<object>(endpoint, new { });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling self-ship appointment for {InboundPlanId}/{ShipmentId}", inboundPlanId, shipmentId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }

    /// <summary>
    /// Get delivery challan document (India marketplace specific)
    /// </summary>
    public async Task<AmazonApiResponse<object>> GetDeliveryChallanDocumentAsync(string inboundPlanId, string shipmentId)
    {
        try
        {
            _logger.LogInformation("Getting delivery challan document for plan {InboundPlanId}, shipment {ShipmentId}", inboundPlanId, shipmentId);
            var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/deliveryChallanDocument";
            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery challan document for {InboundPlanId}/{ShipmentId}", inboundPlanId, shipmentId);
            return new AmazonApiResponse<object>
            {
                Errors = new List<ApiError> { new() { Code = "InternalError", Message = ex.Message } }
            };
        }
    }
}