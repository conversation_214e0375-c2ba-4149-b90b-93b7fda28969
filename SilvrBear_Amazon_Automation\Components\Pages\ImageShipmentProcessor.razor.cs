﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.JSInterop;
using Microsoft.Extensions.Configuration;
using SilvrBear_Amazon_Automation.Constants;
using SilvrBear_Amazon_Automation.Helpers;
using SilvrBear_Amazon_Automation.Models;
using SilvrBear_Amazon_Automation.Services;

namespace SilvrBear_Amazon_Automation.Components.Pages
{
    public partial class ImageShipmentProcessor : ComponentBase
    {
        [Inject] private IImageToShipmentService ImageToShipmentService { get; set; } = default!;
        [Inject] private IProductMappingService ProductMappingService { get; set; } = default!;
        [Inject] private IInboundShipmentService InboundShipmentService { get; set; } = default!;
        [Inject] private ILogger<ImageShipmentProcessor> Logger { get; set; } = default!;
        [Inject] private IJSRuntime JSRuntime { get; set; } = default!;
        [Inject] private IConfiguration Configuration { get; set; } = default!;

        private IBrowserFile? _selectedImageFile;
        private string _shipmentName = "";
        private string _selectedFulfillmentCenter = ShipmentConstants.FulfillmentCenters.DefaultFulfillmentCenterId;
        private string _statusMessage = "";
        private bool _isSuccess = false;
        private bool _isProcessing = false;
        private bool _isValidated = false;
        private bool _isDataProcessed = false;
        private string _currentOperation = "";

        private ImageToShipmentResult? _processingResult;
        // Note: Statistics variables removed as part of cleanup

        protected override async Task OnInitializedAsync()
        {
            try
            {
                // Note: Statistics loading removed as part of cleanup
                Logger.LogInformation("Image to Shipment Processor initialized");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing component");
                SetStatusMessage("Error loading initial data", false);
            }
        }

        private void HandleImageSelection(InputFileChangeEventArgs e)
        {
            _selectedImageFile = e.File;
            _isValidated = false;
            _isDataProcessed = false;
            _processingResult = null;

            if (_selectedImageFile != null)
            {
                SetStatusMessage($"Image selected: {_selectedImageFile.Name}", true);
                Logger.LogInformation("Image file selected: {FileName}, Size: {Size}",
                    _selectedImageFile.Name, _selectedImageFile.Size);
            }
            else
            {
                SetStatusMessage("No image selected", false);
                Logger.LogInformation("No image file selected");
            }

            // Force UI update
            StateHasChanged();
        }

        private async Task ValidateImage()
        {
            if (_selectedImageFile == null)
            {
                SetStatusMessage("Please select an image file first.", false);
                return;
            }

            try
            {
                _isProcessing = true;
                _currentOperation = "validate";
                SetStatusMessage("Extracting data from image...", true);

                Logger.LogInformation("Starting JSON extraction for file: {FileName}", _selectedImageFile.Name);

                var formFile = new BrowserFileAdapter(_selectedImageFile);
                _processingResult = await ImageToShipmentService.ExtractJsonFromImageAsync(
                    formFile,
                    _shipmentName,
                    _selectedFulfillmentCenter);

                if (_processingResult.IsSuccess && _processingResult.JsonExtracted)
                {
                    _isValidated = true;
                    SetStatusMessage($"JSON extraction successful! Found {_processingResult.ValidBoxes} boxes. You can now edit the data before processing.", true);
                    Logger.LogInformation("JSON extraction successful for file: {FileName}. Found {BoxCount} boxes",
                        _selectedImageFile.Name, _processingResult.ExtractedBoxData.Count);
                }
                else
                {
                    var errorMessage = string.Join("; ", _processingResult.Errors);
                    SetStatusMessage($"JSON extraction failed: {errorMessage}", false);
                    Logger.LogWarning("JSON extraction failed for file: {FileName}. Errors: {Errors}",
                        _selectedImageFile.Name, errorMessage);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error extracting JSON from image: {FileName}", _selectedImageFile?.Name ?? "Unknown");
                SetStatusMessage($"JSON extraction error: {ex.Message}", false);
            }
            finally
            {
                _isProcessing = false;
                _currentOperation = "";
                StateHasChanged();
            }
        }

        private async Task ProcessImageOnly()
        {
            if (_processingResult == null || !_processingResult.JsonExtracted || !_processingResult.ExtractedBoxData.Any())
            {
                SetStatusMessage("Please extract JSON data first.", false);
                return;
            }

            try
            {
                _isProcessing = true;
                _currentOperation = "process";
                SetStatusMessage("Processing extracted data...", true);

                Logger.LogInformation("Starting data processing with {BoxCount} extracted boxes", _processingResult.ExtractedBoxData.Count);

                // Use the edited JSON data for processing
                var updatedResult = await ImageToShipmentService.ProcessExtractedDataAsync(
                    _processingResult.ExtractedBoxData,
                    _shipmentName,
                    _selectedFulfillmentCenter);

                // Merge results while preserving the extracted JSON data
                _processingResult.ImageProcessingResult = updatedResult.ImageProcessingResult;
                _processingResult.ConsolidatedItems = updatedResult.ConsolidatedItems;
                _processingResult.ConsolidatedBoxes = updatedResult.ConsolidatedBoxes;
                _processingResult.UniqueSkuCount = updatedResult.UniqueSkuCount;
                _processingResult.TotalShipmentWeightKg = updatedResult.TotalShipmentWeightKg;
                _processingResult.ValidBoxes = updatedResult.ValidBoxes;
                _processingResult.InvalidBoxes = updatedResult.InvalidBoxes;
                _processingResult.DataProcessed = updatedResult.DataProcessed;
                _processingResult.Errors.AddRange(updatedResult.Errors);
                _processingResult.Warnings.AddRange(updatedResult.Warnings);

                if (updatedResult.IsSuccess)
                {
                    _isDataProcessed = true;
                    _processingResult.IsSuccess = true;
                    SetStatusMessage($"Data processing successful! Found {_processingResult.UniqueSkuCount} unique SKUs in {_processingResult.ValidBoxes} boxes.", true);
                    Logger.LogInformation("Data processing successful. {ValidBoxes} valid boxes, {UniqueSkus} unique SKUs",
                        _processingResult.ValidBoxes, _processingResult.UniqueSkuCount);
                }
                else
                {
                    var errorMessage = string.Join("; ", updatedResult.Errors);
                    SetStatusMessage($"Data processing failed: {errorMessage}", false);
                    Logger.LogWarning("Data processing failed. Errors: {Errors}", errorMessage);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing extracted data: {Error}", ex.Message);
                SetStatusMessage($"Data processing error: {ex.Message}", false);
            }
            finally
            {
                _isProcessing = false;
                _currentOperation = "";
                StateHasChanged();
            }
        }

        private async Task CreateShipment()
        {
            // TEST: Add immediate service call to verify it works (uncomment to test)
            bool runTest = false; // Set to true to run the test, false for normal flow
            
            if (runTest)
            {
                Console.WriteLine("*** BLAZOR: About to test service call ***");
                Logger.LogError("*** BLAZOR: About to test service call - LOG ERROR ***");

                // Quick test call with minimal data
                try
                {
                    var testRequest = new CreateInboundShipmentRequest
                    {
                        ShipmentName = "TEST-SHIPMENT",
                        DestinationFulfillmentCenterId = "IND6",
                        Items = new List<ShipmentItem>
                        {
                            new ShipmentItem
                            {
                                Sku = "TEST-SKU",
                                Quantity = 1,
                                ProductName = "Test Product",
                                Condition = "NewItem",
                                UnitCost = 0
                            }
                        },
                        Boxes = new List<ShipmentBox>(),
                        LabelPrepPreference = "SELLER_LABEL"
                    };

                    Console.WriteLine("*** BLAZOR: Calling service method ***");
                    var testResponse = await InboundShipmentService.CreateCompleteInboundShipmentAsync(testRequest);
                    Console.WriteLine($"*** BLAZOR: Service call completed. Response is null: {testResponse == null} ***");
                    
                    if (testResponse != null)
                    {
                        Console.WriteLine($"*** BLAZOR: Test response - ShipmentId: '{testResponse.ShipmentId}', InboundPlanId: '{testResponse.InboundPlanId}' ***");
                    }

                    SetStatusMessage("TEST: Service call completed - check console output", true);
                    return;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"*** BLAZOR: Service call failed: {ex.Message} ***");
                    Logger.LogError(ex, "*** BLAZOR: Service call failed ***");
                    SetStatusMessage($"TEST: Service call failed - {ex.Message}", false);
                    return;
                }
            }

            // Validate that all previous steps have been completed
            if (!_isDataProcessed)
            {
                SetStatusMessage("Please validate and process the image data first before creating shipment.", false);
                return;
            }

            if (_processingResult == null || 
                _processingResult.ConsolidatedItems == null || 
                !_processingResult.ConsolidatedItems.Any())
            {
                SetStatusMessage("No processed data available. Please validate and process the image first.", false);
                return;
            }

            try
            {
                _isProcessing = true;
                _currentOperation = "create";
                SetStatusMessage("Creating Amazon FBA shipment...", true);

                Logger.LogInformation("Creating shipment from processed data with {ItemCount} items", 
                    _processingResult.ConsolidatedItems.Count);

                // Convert consolidated data to shipment request (using correct format from working version)
                var shipmentRequest = new CreateInboundShipmentRequest
                {
                    ShipmentName = !string.IsNullOrWhiteSpace(_shipmentName) ? _shipmentName : $"IMG-Shipment-{DateTime.UtcNow:yyyyMMdd-HHmmss}",
                    DestinationFulfillmentCenterId = _selectedFulfillmentCenter,
                    Items = _processingResult.ConsolidatedItems.Select(item => new ShipmentItem
                    {
                        Sku = item.Sku,
                        Quantity = item.TotalQuantity,
                        ProductName = item.ProductName,
                        //Condition = "NewItem", // Use constant from working version
                        //UnitCost = 0 // Set as in working version
                    }).ToList(),
                    Boxes = _processingResult.ConsolidatedBoxes?.Select(box => new ShipmentBox
                    {
                        BoxId = $"BOX-{box.BoxNumber:D3}",
                        Dimensions = new BoxDimensions
                        {
                            Length = box.Dimensions.Length,
                            Width = box.Dimensions.Width,
                            Height = box.Dimensions.Height,
                            Unit = "cm" // Explicit unit as in working version
                        },
                        Weight = new BoxWeight
                        {
                            Value = box.TotalWeightKg, // Already converted to pounds
                            Unit = "kg"
                        },
                        Contents = box.Items.Select(item => new BoxContent
                        {
                            Sku = item.Sku,
                            Quantity = item.Quantity
                        }).ToList()
                    }).ToList() ?? new List<ShipmentBox>(),
                    LabelPrepPreference = ShipmentConstants.AmazonAPI.DefaultLabelPrepPreference, // Use constant
                    //set LabelPrepPreference using LabelPrepPreference from appsettings.json

                    ShipFromAddress = CreateShipFromAddressString() // Add missing field from working version
                };

                // Use the dedicated InboundShipmentService to create the shipment
                Console.WriteLine("*** BLAZOR NORMAL FLOW: About to call InboundShipmentService ***");
                Logger.LogInformation("DEBUG: About to call InboundShipmentService.CreateCompleteInboundShipmentAsync");
                Logger.LogInformation("DEBUG: Service type is: {ServiceType}", InboundShipmentService.GetType().FullName);
                Logger.LogInformation("DEBUG: Shipment request - Name: {Name}, ItemCount: {ItemCount}", 
                    shipmentRequest.ShipmentName, shipmentRequest.Items?.Count ?? 0);
                
                // Make the actual service call
                Console.WriteLine("*** BLAZOR NORMAL FLOW: Making service call now ***");
                var shipmentResponse = await InboundShipmentService.CreateCompleteInboundShipmentAsync(shipmentRequest);
                
                // if shipmenent response contains any errors log errors to console
                if (shipmentResponse?.Errors != null && shipmentResponse.Errors.Any())
                {
                    Console.WriteLine($"*** BLAZOR NORMAL FLOW: Shipment creation had errors: {string.Join(", ", shipmentResponse.Errors)} ***");
                    Logger.LogWarning("Shipment creation had errors: {Errors}", string.Join(", ", shipmentResponse.Errors));
                }
                
                Console.WriteLine("*** BLAZOR NORMAL FLOW: Service call completed ***");
                Logger.LogInformation("DEBUG: InboundShipmentService call completed");

                
                // DEBUG: Log the raw response object
                Logger.LogInformation("DEBUG: Response object - Type: {Type}, IsNull: {IsNull}", 
                    shipmentResponse?.GetType().Name ?? "null", shipmentResponse == null);
                if (shipmentResponse != null)
                {
                    Logger.LogInformation("DEBUG: Response details - ShipmentId: '{ShipmentId}', InboundPlanId: '{InboundPlanId}', ErrorCount: {ErrorCount}",
                        shipmentResponse.ShipmentId ?? "NULL",
                        shipmentResponse.InboundPlanId ?? "NULL",
                        shipmentResponse.Errors?.Count ?? -1);
                }

                // Debug: Log the complete response for troubleshooting
                Logger.LogInformation("Shipment response received. ShipmentId: {ShipmentId}, InboundPlanId: {InboundPlanId}, Errors: {Errors}",
                    shipmentResponse?.ShipmentId ?? "null",
                    shipmentResponse?.InboundPlanId ?? "null", 
                    string.Join(", ", shipmentResponse?.Errors ?? new List<string>()));

                // Check if we have any shipment identifier (full success or partial success with plan ID)
                var hasShipmentId = !string.IsNullOrEmpty(shipmentResponse?.ShipmentId);
                var hasInboundPlanId = !string.IsNullOrEmpty(shipmentResponse?.InboundPlanId);
                
                if (shipmentResponse != null && (hasShipmentId || hasInboundPlanId))
                {
                    var shipmentIdentifier = hasShipmentId ? shipmentResponse.ShipmentId : shipmentResponse.InboundPlanId;
                    
                    if ((shipmentResponse.Errors?.Count ?? 0) == 0)
                    {
                        // Full success - both plan and shipment created
                        SetStatusMessage($"Shipment created successfully! ID: {shipmentIdentifier}", true);
                        Logger.LogInformation("Shipment created successfully. Identifier: {ShipmentIdentifier}", 
                            shipmentIdentifier);
                    }
                    else
                    {
                        // Partial success - plan created but shipment creation failed
                        SetStatusMessage($"Inbound plan created successfully! ID: {shipmentIdentifier}. Note: {string.Join("; ", shipmentResponse.Errors ?? new List<string>())}", true);
                        Logger.LogInformation("Inbound plan created successfully but shipment creation had issues. Plan ID: {PlanId}, Errors: {Errors}", 
                            shipmentIdentifier, string.Join("; ", shipmentResponse.Errors ?? new List<string>()));
                    }

                    // Update the processing result with shipment details
                    if (_processingResult != null)
                    {
                        _processingResult.ShipmentId = shipmentIdentifier;
                        _processingResult.ShipmentResponse = shipmentResponse;
                        _processingResult.IsSuccess = true;
                    }

                    // Reset form for next image
                    _selectedImageFile = null;
                    _shipmentName = "";
                    _isValidated = false;
                    _isDataProcessed = false;
                }
                else
                {
                    var errorMessage = (shipmentResponse?.Errors?.Count ?? 0) > 0 
                        ? string.Join("; ", shipmentResponse?.Errors ?? new List<string>())
                        : "Unknown error occurred during shipment creation";
                    SetStatusMessage($"Shipment creation failed: {errorMessage}", false);
                    Logger.LogWarning("Shipment creation failed. Errors: {Errors}", errorMessage);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error creating shipment: {Error}", ex.Message);
                SetStatusMessage($"Shipment creation error: {ex.Message}", false);
            }
            finally
            {
                _isProcessing = false;
                _currentOperation = "";
                StateHasChanged();
            }
        }

        // Note: Removed unused methods (RefreshMapping, ShowSampleImage, ShowStatistics) as part of cleanup

        // Note: Statistics loading methods removed as part of cleanup

        private void SetStatusMessage(string message, bool success)
        {
            _statusMessage = message;
            _isSuccess = success;
        }

        private void ClearStatus()
        {
            _statusMessage = "";
        }

        private static string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
            return $"{bytes / (1024.0 * 1024.0):F1} MB";
        }

        private async Task OnJsonDataChanged()
        {
            // Mark that data has been changed
            if (_processingResult != null)
            {
                _processingResult.JsonEdited = true;
            }
            await Task.CompletedTask;
        }

        // Note: CalculateSuccessRate method removed as part of cleanup

        private async Task OnJsonDataSaved(List<BoxDataRow> updatedData)
        {
            if (_processingResult != null)
            {
                _processingResult.ExtractedBoxData = updatedData;
                _processingResult.JsonEdited = true;
                SetStatusMessage("JSON data saved successfully. You can now process the data.", true);
                Logger.LogInformation("JSON data saved with {BoxCount} boxes", updatedData.Count);
            }
            await Task.CompletedTask;
        }

        private string CreateShipFromAddressString()
        {
            return $"{Configuration["ShipFromAddress:Name"]}, " +
                   $"{Configuration["ShipFromAddress:AddressLine1"]}, " +
                   $"{Configuration["ShipFromAddress:AddressLine2"]}, " +
                   $"{Configuration["ShipFromAddress:City"]}, " +
                   $"{Configuration["ShipFromAddress:StateOrProvince"]} " +
                   $"{Configuration["ShipFromAddress:PostalCode"]}, " +
                   $"{Configuration["ShipFromAddress:CountryCode"]}";
        }
    }
}
