@page "/image-shipment"
@rendermode @(InteractiveServer)
@using SilvrBear_Amazon_Automation.Models
@using SilvrBear_Amazon_Automation.Services
@using SilvrBear_Amazon_Automation.Constants
@using SilvrBear_Amazon_Automation.Helpers
@using SilvrBear_Amazon_Automation.Components.Shared
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web

<PageTitle>Image to Shipment Processor</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="mb-4">
                <h2>
                    <i class="bi bi-camera"></i> Image to Shipment Processor
                </h2>
                <p class="text-muted mb-0">Upload a shipment image, validate the data, and create Amazon FBA inbound shipments</p>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(_statusMessage))
    {
        <div class="alert @(_isSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show" role="alert">
            @_statusMessage
            <button type="button" class="btn-close" @onclick="ClearStatus"></button>
        </div>
    }

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-upload"></i> Upload Shipment Image
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Shipment Name</label>
                                <input type="text" class="form-control" @bind="_shipmentName" 
                                       placeholder="Enter shipment name (optional)" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Fulfillment Center</label>
                                <select class="form-select" @bind="_selectedFulfillmentCenter">
                                    @foreach (var fc in ShipmentConstants.FulfillmentCenters.IndiaFulfillmentCenters)
                                    {
                                        <option value="@fc.Key" selected="@(fc.Key == ShipmentConstants.FulfillmentCenters.DefaultFulfillmentCenterId)">
                                            @fc.Value
                                        </option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Image File</label>
                        <InputFile OnChange="HandleImageSelection" class="form-control" accept="image/*" />
                        <div class="form-text">
                            Supported formats: JPG, PNG, BMP, GIF. Maximum size: @(ShipmentConstants.FileProcessing.MaxImageSizeMB)MB
                        </div>
                    </div>

                    @if (_selectedImageFile != null)
                    {
                        <div class="mb-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Selected Image</h6>
                                    <p class="card-text">
                                        <strong>File:</strong> @_selectedImageFile.Name<br />
                                        <strong>Size:</strong> @FormatFileSize(_selectedImageFile.Size)<br />
                                        <strong>Type:</strong> @_selectedImageFile.ContentType
                                    </p>                    <div class="small text-muted mt-2">
                        <strong>Debug Info:</strong><br />
                        selectedImageFile != null: @(_selectedImageFile != null)<br />
                        isProcessing: @_isProcessing<br />
                        isValidated: @_isValidated
                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" @onclick="ValidateImage"
                                disabled="@(_selectedImageFile == null || _isProcessing)">
                            @if (_isProcessing && _currentOperation == "validate")
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            <i class="bi bi-check-circle"></i> 1. Extract Data from Image
                        </button>

                        <button type="button" class="btn btn-warning" @onclick="ProcessImageOnly"
                                disabled="@(_selectedImageFile == null || _isProcessing || !_isValidated)">
                            @if (_isProcessing && _currentOperation == "process")
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            <i class="bi bi-gear"></i> 2. Process & Map Data
                        </button>

                        <button type="button" class="btn btn-primary" @onclick="CreateShipment"
                                disabled="@(_selectedImageFile == null || _isProcessing || !_isDataProcessed)">
                            @if (_isProcessing && _currentOperation == "create")
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            <i class="bi bi-box-arrow-up"></i> 3. Create Amazon Shipment
                        </button>
                    </div>

                    @if (_isProcessing)
                    {
                        <div class="mt-3">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 100%">
                                    Processing @_currentOperation...
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>

            @if (_processingResult != null && _processingResult.JsonExtracted)
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pencil-square"></i> Edit Extracted Data
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Review and edit the extracted data below.</strong>
                            Make any necessary corrections before processing the data.
                        </div>

                        @if (_processingResult.ExtractedBoxData != null && _processingResult.ExtractedBoxData.Any())
                        {
                            <JsonTableEditor BoxData="_processingResult.ExtractedBoxData"
                                           OnDataSaved="OnJsonDataSaved"
                                           OnDataChanged="OnJsonDataChanged" />
                        }
                        else
                        {
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>Debug Info:</strong>
                                ExtractedBoxData is @(_processingResult.ExtractedBoxData == null ? "null" : $"empty (count: {_processingResult.ExtractedBoxData.Count})")
                                <br />
                                JsonExtracted: @_processingResult.JsonExtracted
                                <br />
                                IsSuccess: @_processingResult.IsSuccess
                                @if (_processingResult.Errors.Any())
                                {
                                    <br />
                                    <strong>Errors:</strong> @string.Join(", ", _processingResult.Errors)
                                }
                            </div>
                        }
                    </div>
                </div>
            }

            @if (_processingResult != null && _processingResult.ImageProcessingResult != null)
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-check"></i> Extracted Box Data
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (_processingResult.ImageProcessingResult.ProcessedBoxes.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Box #</th>
                                            <th>Original Product</th>
                                            <th>Mapped Product</th>
                                            <th>SKU</th>
                                            <th>Qty</th>
                                            <th>Unit Weight (kg)</th>
                                            <th>Dimensions</th>
                                            <th>Total Weight (kg)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var box in _processingResult.ImageProcessingResult.ProcessedBoxes)
                                        {
                                            <tr class="@(box.IsValid ? "" : "table-warning")">
                                                <td>@box.BoxNumber</td>
                                                <td>@box.OriginalProductName</td>
                                                <td>@box.MappedProductName</td>
                                                <td><code>@box.GeneratedSKU</code></td>
                                                <td>@box.Quantity</td>
                                                <td><strong>@box.ItemWeightKg.ToString("F3")</strong></td>
                                                <td>@box.DimensionsString</td>
                                                <td>@box.TotalItemWeightKg.ToString("F2")</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center text-muted py-4">
                                <i class="bi bi-exclamation-circle display-4"></i>
                                <p class="mt-2">No boxes extracted from the image</p>
                            </div>
                        }
                    </div>
                </div>
            }

            @if (_processingResult != null && _processingResult.ConsolidatedItems.Any())
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul"></i> Item Details List (for Amazon API)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>SKU</th>
                                        <th>Product Name</th>
                                        <th>Total Quantity</th>
                                        <th>Unit Weight (kg)</th>
                                        <th>Total Weight (kg)</th>
                                        <th>Barcode Type</th>
                                        <th>Source Boxes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in _processingResult.ConsolidatedItems)
                                    {
                                        <tr>
                                            <td><code>@item.Sku</code></td>
                                            <td>@item.ProductName</td>
                                            <td><strong>@item.TotalQuantity</strong></td>
                                            <td>@item.UnitWeightKg.ToString("F3")</td>
                                            <td><strong>@item.TotalWeightKg.ToString("F2")</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(item.BarcodeType))
                                                {
                                                    <span class="badge bg-secondary">@item.BarcodeType</span>
                                                }
                                            </td>
                                            <td>@string.Join(", ", item.SourceBoxNumbers)</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <strong>Summary:</strong> @_processingResult.UniqueSkuCount unique SKUs,
                                Total Weight: @_processingResult.TotalShipmentWeightKg.ToString("F2") kg
                            </small>
                        </div>
                    </div>
                </div>
            }

            @if (_processingResult != null && _processingResult.ConsolidatedBoxes.Any())
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-box"></i> Box Details List (for Amazon API)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>Box #</th>
                                        <th>Weight (kg)</th>
                                        <th>Weight (lbs)</th>
                                        <th>Dimensions</th>
                                        <th>Contents</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var box in _processingResult.ConsolidatedBoxes)
                                    {
                                        <tr>
                                            <td>@box.BoxNumber</td>
                                            <td><strong>@box.TotalWeightKg.ToString("F2")</strong></td>
                                            <td>@box.TotalWeightPounds.ToString("F2")</td>
                                            <td>@box.DimensionsString</td>
                                            <td>
                                                @if (box.Items.Any())
                                                {
                                                    @foreach (var item in box.Items.Select((item, index) => new { item, index }))
                                                    {
                                                        <span class="small">
                                                            <code>@item.item.Sku</code>: @item.item.Quantity pcs 
                                                            <span class="text-muted">(@item.item.UnitWeightKg.ToString("F3") kg each)</span>
                                                            <span class="text-muted">Total: @item.item.TotalWeightKg.ToString("F2") kg</span>@(item.index < box.Items.Count - 1 ? ", " : "")
                                                        </span>
                                                    }
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
        </div>

        <div class="col-lg-4">
            <!-- Product Mapping Status removed as part of UI cleanup -->

            @if (_processingResult != null)
            {
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-info-circle"></i> Processing Result
                        </h6>
                    </div>
                    <div class="card-body">
                        @if (_processingResult.IsSuccess)
                        {
                            <div class="text-success mb-2">
                                <i class="bi bi-check-circle"></i> Processing successful
                            </div>
                            
                            @if (!string.IsNullOrEmpty(_processingResult.ShipmentId))
                            {
                                <div class="alert alert-success">
                                    <strong>Shipment Created!</strong><br />
                                    <strong>ID:</strong> @_processingResult.ShipmentId<br />
                                    <strong>Time:</strong> @_processingResult.ProcessedAt.ToString("HH:mm:ss")
                                </div>
                            }

                            <ul class="list-unstyled small">
                                <li><strong>Total Boxes:</strong> @_processingResult.TotalBoxesProcessed</li>
                                <li><strong>Valid Boxes:</strong> @_processingResult.ValidBoxes</li>
                                <li><strong>Invalid Boxes:</strong> @_processingResult.InvalidBoxes</li>
                                <li><strong>Processing Time:</strong> @_processingResult.TotalProcessingTime.TotalSeconds.ToString("F1")s</li>
                            </ul>
                        }
                        else
                        {
                            <div class="text-danger mb-2">
                                <i class="bi bi-exclamation-circle"></i> Processing failed
                            </div>
                        }

                        @if (_processingResult.Errors.Any())
                        {
                            <div class="mt-2">
                                <strong class="text-danger">Errors:</strong>
                                <ul class="list-unstyled small">
                                    @foreach (var error in _processingResult.Errors)
                                    {
                                        <li class="text-danger">• @error</li>
                                    }
                                </ul>
                            </div>
                        }

                        @if (_processingResult.Warnings.Any())
                        {
                            <div class="mt-2">
                                <strong class="text-warning">Warnings:</strong>
                                <ul class="list-unstyled small">
                                    @foreach (var warning in _processingResult.Warnings)
                                    {
                                        <li class="text-warning">• @warning</li>
                                    }
                                </ul>
                            </div>
                        }
                    </div>
                </div>
            }

            <!-- Processing Statistics removed as part of UI cleanup -->
        </div>
    </div>
</div>


