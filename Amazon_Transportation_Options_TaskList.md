# Amazon Transportation Options Implementation Task List

## **Current Issue Analysis**

### **Problem Identified from Logs (Line 1396+)**
The `generateTransportationOptions` API call is failing with **400 Bad Request** due to:

1. **Missing placementOptionId**: 
   - Error: `Value '' at 'request.placementOptionId' failed to satisfy constraint: Member must have length greater than or equal to 38`
   - **Available placementOptionId**: `pl6a41dcb5-6256-4f4c-86b9-488089212e7d` (from logs line 634, 679, 705, 1369)

2. **Missing shipmentTransportationConfigurations**: 
   - Error: `Value '[]' at 'request.shipmentTransportationConfigurations' failed to satisfy constraint: Member must have length greater than or equal to 1`
   - **Required**: Contact information, readyToShipWindow, shipmentId

### **Available Data from Previous API Calls**
- **InboundPlanId**: `wf3abbf5f8-6471-4518-bbab-8897e04f6463`
- **ShipmentId**: `sh96bbd000-2630-4406-872c-61e409987c42`
- **PlacementOptionId**: `pl6a41dcb5-6256-4f4c-86b9-488089212e7d`

---

## **TASK 1: Add Contact Information to ShipmentConstants.cs**

### **1.1 Create Transportation Contact Information Section**
**File**: `SilvrBear_Amazon_Automation/Constants/ShipmentConstants.cs`

**Add new section after line 213 (after AmazonAPI section)**:

```csharp
/// <summary>
/// Transportation and contact information for Amazon shipments
/// </summary>
public static class Transportation
{
    /// <summary>
    /// Contact information for shipment transportation
    /// User will edit these values with correct information later
    /// </summary>
    public static class ContactInformation
    {
        public const string Email = "<EMAIL>";
        public const string Name = "Contact Name";
        public const string PhoneNumber = "+1234567890";
    }
    
    /// <summary>
    /// Ready to ship window configuration
    /// </summary>
    public static class ReadyToShipWindow
    {
        /// <summary>
        /// Number of days from tomorrow to set as start date
        /// </summary>
        public const int StartDateOffsetDays = 1; // Tomorrow
        
        /// <summary>
        /// Number of days from start date for end date
        /// </summary>
        public const int WindowDurationDays = 7; // 1 week window
    }
}
```

**Acceptance Criteria**:
- ✅ Contact information fields added with placeholder values
- ✅ Ready to ship window configuration added
- ✅ User can easily edit contact values later
- ✅ Build compiles successfully

---

## **TASK 2: Create Transportation Request Models**

### **2.1 Create Transportation Request DTOs**
**File**: `SilvrBear_Amazon_Automation/Models/Transportation/` (new directory)

**Create files**:
1. `GenerateTransportationOptionsRequest.cs`
2. `ShipmentTransportationConfiguration.cs`
3. `ContactInformation.cs`
4. `ReadyToShipWindow.cs`
5. `TransportationOptionsResponse.cs`

**Models Structure**:
```csharp
// GenerateTransportationOptionsRequest.cs
public class GenerateTransportationOptionsRequest
{
    public string PlacementOptionId { get; set; }
    public List<ShipmentTransportationConfiguration> ShipmentTransportationConfigurations { get; set; }
}

// ShipmentTransportationConfiguration.cs
public class ShipmentTransportationConfiguration
{
    public string ShipmentId { get; set; }
    public ContactInformation ContactInformation { get; set; }
    public ReadyToShipWindow ReadyToShipWindow { get; set; }
}

// ContactInformation.cs
public class ContactInformation
{
    public string Email { get; set; }
    public string Name { get; set; }
    public string PhoneNumber { get; set; }
}

// ReadyToShipWindow.cs
public class ReadyToShipWindow
{
    public DateTime Start { get; set; }
    public DateTime End { get; set; }
}

// TransportationOptionsResponse.cs
public class TransportationOptionsResponse
{
    public List<TransportationOption> TransportationOptions { get; set; }
    public string OperationId { get; set; }
}

public class TransportationOption
{
    public string TransportationOptionId { get; set; }
    public string CarrierName { get; set; }
    public string ServiceName { get; set; }
    public decimal Cost { get; set; }
    public string Currency { get; set; }
    public DateTime EstimatedDeliveryDate { get; set; }
}
```

**Acceptance Criteria**:
- ✅ All transportation models created
- ✅ Models match Amazon API documentation requirements
- ✅ Proper JSON serialization attributes added
- ✅ Build compiles successfully

---

## **TASK 3: Update GenerateTransportationOptionsAsync Method**

### **3.1 Modify AmazonSellerApiService.GenerateTransportationOptionsAsync**
**File**: `SilvrBear_Amazon_Automation/Services/AmazonSellerApiService.cs`
**Method**: `GenerateTransportationOptionsAsync` (around line 981)

**Current Issue**: Method sends empty payload `new { }`

**Required Changes**:
1. **Add parameters**: `placementOptionId`, `shipmentId`
2. **Build proper request payload** with:
   - placementOptionId
   - shipmentTransportationConfigurations array
   - Contact information from constants
   - ReadyToShipWindow with tomorrow's date

**New Method Signature**:
```csharp
public async Task<AmazonApiResponse<object>> GenerateTransportationOptionsAsync(
    string inboundPlanId, 
    string placementOptionId, 
    string shipmentId)
```

**Request Payload Structure**:
```csharp
var request = new GenerateTransportationOptionsRequest
{
    PlacementOptionId = placementOptionId,
    ShipmentTransportationConfigurations = new List<ShipmentTransportationConfiguration>
    {
        new ShipmentTransportationConfiguration
        {
            ShipmentId = shipmentId,
            ContactInformation = new ContactInformation
            {
                Email = ShipmentConstants.Transportation.ContactInformation.Email,
                Name = ShipmentConstants.Transportation.ContactInformation.Name,
                PhoneNumber = ShipmentConstants.Transportation.ContactInformation.PhoneNumber
            },
            ReadyToShipWindow = new ReadyToShipWindow
            {
                Start = DateTime.UtcNow.AddDays(ShipmentConstants.Transportation.ReadyToShipWindow.StartDateOffsetDays),
                End = DateTime.UtcNow.AddDays(ShipmentConstants.Transportation.ReadyToShipWindow.StartDateOffsetDays + 
                                            ShipmentConstants.Transportation.ReadyToShipWindow.WindowDurationDays)
            }
        }
    }
};
```

**Acceptance Criteria**:
- ✅ Method accepts required parameters
- ✅ Builds proper request payload with all required fields
- ✅ Uses contact information from ShipmentConstants
- ✅ Sets readyToShipWindow start date to tomorrow
- ✅ Returns operationId for polling

---

## **TASK 4: Add Operation Polling for Transportation Generation**

### **4.1 Update GenerateTransportationOptionsAsync to Poll Operation Status**
**File**: `SilvrBear_Amazon_Automation/Services/AmazonSellerApiService.cs`

**Required Changes**:
1. **Use PostWithOperationIdAsync** instead of PostAsync
2. **Poll operation status** until SUCCESS
3. **Handle operation failures** appropriately

**Implementation Pattern** (similar to existing placement option confirmation):
```csharp
// Use specialized method to extract operationId from response
var (result, operationId) = await _apiClient.PostWithOperationIdAsync(endpoint, request);

if (result.IsSuccess && !string.IsNullOrEmpty(operationId))
{
    // Poll for operation completion before proceeding
    _logger.LogInformation("POLLING: Starting operation status polling for operationId: {OperationId}", operationId);
    var (pollSuccess, pollError) = await PollOperationStatusAsync(operationId, "transportation options generation");
    if (!pollSuccess)
    {
        _logger.LogWarning("Transportation options generation operation failed: {Error}", pollError);
        return new AmazonApiResponse<object>
        {
            Errors = new List<ApiError> { new() { Code = "TransportationGenerationOperationFailed", Message = pollError ?? "Transportation options generation operation failed" } }
        };
    }
}
```

**Acceptance Criteria**:
- ✅ Uses PostWithOperationIdAsync for operation tracking
- ✅ Polls operation status until completion
- ✅ Handles operation failures gracefully
- ✅ Logs operation progress appropriately

---

## **TASK 5: Update ListTransportationOptionsAsync Method**

### **5.1 Add Query Parameters Support**
**File**: `SilvrBear_Amazon_Automation/Services/AmazonSellerApiService.cs`
**Method**: `ListTransportationOptionsAsync` (around line 1001)

**Current Issue**: Method doesn't accept placementOptionId query parameter

**Required Changes**:
1. **Add placementOptionId parameter**
2. **Build query string** with placementOptionId
3. **Update return type** to proper transportation options response

**New Method Signature**:
```csharp
public async Task<AmazonApiResponse<TransportationOptionsResponse>> ListTransportationOptionsAsync(
    string inboundPlanId, 
    string placementOptionId)
```

**Implementation**:
```csharp
var endpoint = $"/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions?placementOptionId={placementOptionId}";
return await _apiClient.GetAsync<TransportationOptionsResponse>(endpoint);
```

**Acceptance Criteria**:
- ✅ Method accepts placementOptionId parameter
- ✅ Builds correct query string
- ✅ Returns properly typed response
- ✅ Follows Amazon API documentation

---

## **TASK 6: Update Amazon Workflow to Pass Required Parameters**

### **6.1 Modify CreateInboundShipmentForIndiaAsync Workflow**
**File**: `SilvrBear_Amazon_Automation/Services/AmazonSellerApiService.cs`
**Method**: `CreateInboundShipmentForIndiaAsync` (around line 750)

**Current Issue**: Workflow calls transportation methods without required parameters

**Required Changes**:
1. **Extract placementOptionId** from placement options response (already available)
2. **Pass placementOptionId and shipmentId** to transportation methods
3. **Store transportation options** for future use

**Updated STEP 7**:
```csharp
// STEP 7: Generate transportation options with required parameters
_logger.LogInformation("STEP 7: Generating transportation options for inbound plan {InboundPlanId}...", inboundPlanId);
var generateTransportResponse = await GenerateTransportationOptionsAsync(inboundPlanId, placementOptionId, finalShipmentId);
```

**Updated STEP 8**:
```csharp
// STEP 8: List transportation options with placementOptionId
_logger.LogInformation("STEP 8: Listing transportation options for inbound plan {InboundPlanId}...", inboundPlanId);
var listTransportResponse = await ListTransportationOptionsAsync(inboundPlanId, placementOptionId);
```

**Acceptance Criteria**:
- ✅ Workflow passes correct parameters to transportation methods
- ✅ Uses placementOptionId from previous steps
- ✅ Uses shipmentId from previous steps
- ✅ Stores transportation options for future use

---

## **TASK 7: Update Interface Definitions**

### **7.1 Update IAmazonSellerApiService Interface**
**File**: `SilvrBear_Amazon_Automation/Services/IAmazonSellerApiService.cs`

**Required Changes**:
1. **Update GenerateTransportationOptionsAsync signature**
2. **Update ListTransportationOptionsAsync signature**
3. **Add proper return types**

**Updated Interface Methods**:
```csharp
/// <summary>
/// Generate transportation options using v2024-03-20 API
/// </summary>
/// <param name="inboundPlanId">Inbound plan ID</param>
/// <param name="placementOptionId">Placement option ID from previous step</param>
/// <param name="shipmentId">Shipment ID from previous step</param>
/// <returns>Transportation options response</returns>
Task<AmazonApiResponse<object>> GenerateTransportationOptionsAsync(string inboundPlanId, string placementOptionId, string shipmentId);

/// <summary>
/// List available transportation options using v2024-03-20 API
/// </summary>
/// <param name="inboundPlanId">Inbound plan ID</param>
/// <param name="placementOptionId">Placement option ID for filtering</param>
/// <returns>Transportation options list</returns>
Task<AmazonApiResponse<TransportationOptionsResponse>> ListTransportationOptionsAsync(string inboundPlanId, string placementOptionId);
```

**Acceptance Criteria**:
- ✅ Interface matches implementation signatures
- ✅ Proper documentation added
- ✅ Return types are correct
- ✅ Build compiles successfully

---

## **TASK 8: Add Transportation Options Storage and Deserialization**

### **8.1 Store Transportation Options in Workflow**
**File**: `SilvrBear_Amazon_Automation/Services/AmazonSellerApiService.cs`

**Required Changes**:
1. **Deserialize transportation options response**
2. **Store options for later use**
3. **Log available options**

**Implementation**:
```csharp
if (listTransportResponse.IsSuccess && listTransportResponse.Payload?.TransportationOptions?.Any() == true)
{
    _logger.LogInformation("STEP 8 SUCCESS: Found {Count} transportation options", 
        listTransportResponse.Payload.TransportationOptions.Count);
    
    foreach (var option in listTransportResponse.Payload.TransportationOptions)
    {
        _logger.LogInformation("Transportation Option: {Id} - {Carrier} {Service} - Cost: {Cost} {Currency}", 
            option.TransportationOptionId, option.CarrierName, option.ServiceName, option.Cost, option.Currency);
    }
    
    // Store for future use (could be added to response payload)
    var storedTransportationOptions = listTransportResponse.Payload.TransportationOptions;
}
```

**Acceptance Criteria**:
- ✅ Transportation options are properly deserialized
- ✅ Options are stored for future operations
- ✅ Available options are logged for visibility
- ✅ Data structure matches Amazon API response

---

## **TASK 9: Testing and Validation**

### **9.1 Build and Test Updated Implementation**
**Required Actions**:
1. **Build solution** to ensure no compilation errors
2. **Test transportation options generation** with real API
3. **Verify operation polling** works correctly
4. **Validate transportation options listing** returns data

**Test Scenarios**:
- ✅ Generate transportation options with valid parameters
- ✅ Poll operation status until completion
- ✅ List transportation options with placementOptionId
- ✅ Verify transportation options are properly deserialized
- ✅ Confirm no 400 Bad Request errors

**Acceptance Criteria**:
- ✅ No build errors
- ✅ Transportation options API calls succeed
- ✅ Operation polling completes successfully
- ✅ Transportation options are retrieved and stored
- ✅ Logs show proper workflow progression

---

## **TASK 10: Documentation and Cleanup**

### **10.1 Update Documentation**
**Files to Update**:
- `Amazon_API_Workflow_Sequence.md`
- `Amazon_API_Workflow_Compliance_TaskList.md`

**Required Updates**:
1. **Document transportation options parameters**
2. **Update workflow sequence** with correct parameter passing
3. **Add transportation options storage** to workflow documentation

**Acceptance Criteria**:
- ✅ Documentation reflects actual implementation
- ✅ Parameter requirements are clearly documented
- ✅ Workflow sequence is accurate
- ✅ Transportation options handling is documented

---

## **SUCCESS CRITERIA**

### **Overall Implementation Success**:
1. ✅ **No 400 Bad Request errors** for generateTransportationOptions
2. ✅ **Proper request payload** with all required fields
3. ✅ **Operation polling** completes successfully
4. ✅ **Transportation options** are retrieved and stored
5. ✅ **Contact information** is configurable in ShipmentConstants
6. ✅ **Ready to ship window** uses tomorrow's date
7. ✅ **All interfaces** match implementations
8. ✅ **Build compiles** without errors
9. ✅ **Workflow progression** is properly logged
10. ✅ **Transportation options** are available for future confirmation

### **Next Steps After Completion**:
- User can edit contact information in ShipmentConstants.cs
- Transportation options will be available for confirmation
- Workflow can proceed to confirmTransportationOptions step
- Self-ship appointment scheduling can be implemented if needed
